<template>
  <div class="tool-layout">
    <!-- 工具头部 -->
    <div class="tool-header sketch-border">
      <div class="header-content">
        <div class="tool-info">
          <h1 class="tool-title">{{ title }}</h1>
          <p class="tool-description">{{ description }}</p>
        </div>
        <div class="tool-actions">
          <slot name="header-actions"></slot>
        </div>
      </div>
      <!-- 装饰星星 -->
      <div class="doodle-star" style="top: 10px; right: 20px;">✨</div>
      <div class="doodle-star" style="bottom: 15px; left: 30px;">⭐</div>
    </div>

    <!-- 工具主体内容 -->
    <div class="tool-body">
      <div class="tool-content">
        <slot></slot>
      </div>
    </div>

    <!-- 工具底部 -->
    <div class="tool-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.tool-layout {
  @apply min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 p-4;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.tool-header {
  @apply bg-white p-6 mb-6 relative overflow-hidden;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
}

.sketch-border {
  border: 2px solid #64748b;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #94a3b8;
  border-radius: 14px;
  pointer-events: none;
}

.header-content {
  @apply flex justify-between items-start;
}

.tool-title {
  @apply text-3xl font-bold text-gray-800 mb-2;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.tool-description {
  @apply text-gray-600 text-lg;
}

.tool-body {
  @apply bg-white rounded-xl p-6;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tool-content {
  @apply space-y-6;
}

.tool-footer {
  @apply mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.doodle-star {
  @apply absolute text-yellow-400 text-xl;
  animation: twinkle 2s infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tool-layout {
    @apply p-2;
  }
  
  .tool-header {
    @apply p-4 mb-4;
  }
  
  .tool-title {
    @apply text-2xl;
  }
  
  .tool-description {
    @apply text-base;
  }
  
  .header-content {
    @apply flex-col space-y-3;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .tool-layout {
    @apply bg-gradient-to-br from-gray-900 to-gray-800;
  }
  
  .tool-header,
  .tool-body {
    @apply bg-gray-800 border-gray-600;
    background: linear-gradient(145deg, #374151, #4b5563);
  }
  
  .tool-title {
    @apply text-gray-100;
  }
  
  .tool-description {
    @apply text-gray-300;
  }
  
  .sketch-border {
    border-color: #6b7280;
  }
  
  .sketch-border::before {
    border-color: #9ca3af;
  }
}
</style>
