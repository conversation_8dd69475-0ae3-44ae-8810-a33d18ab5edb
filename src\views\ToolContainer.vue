<template>
  <div class="tool-container">
    <!-- 工具头部 -->
    <div class="tool-header">
      <div class="tool-header-left">
        <button @click="goBack" class="back-button">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          返回
        </button>
        <h1 class="tool-title">{{ toolInfo?.name || '工具加载中...' }}</h1>
      </div>
      <div class="tool-header-right">
        <button @click="refreshTool" class="action-button" title="刷新">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M3.51 15A9 9 0 0 0 18.36 18.36L23 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <button @click="openInNewTab" class="action-button" title="在新标签页打开">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 13V19A2 2 0 0 1 16 21H5A2 2 0 0 1 3 19V8A2 2 0 0 1 5 6H11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M15 3H21V9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10 14L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 工具内容区域 -->
    <div class="tool-content">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>工具加载中...</p>
      </div>

      <div v-if="error" class="error-overlay">
        <div class="error-message">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M15 9L9 15M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <p>{{ error }}</p>
          <button @click="retryLoad" class="retry-button">重试</button>
        </div>
      </div>

      <!-- Vue 组件渲染 -->
      <component
        v-if="!loading && !error && toolComponent"
        :is="toolComponent"
        class="tool-component"
      />

      <!-- 降级到 iframe（用于未迁移的工具） -->
      <iframe
        v-else-if="!loading && !error && toolInfo?.url"
        ref="toolFrame"
        :src="toolInfo.url"
        class="tool-iframe"
        sandbox="allow-scripts allow-same-origin allow-forms allow-downloads allow-popups"
        @load="onToolLoad"
        @error="onToolError"
      ></iframe>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRouter } from 'vue-router'
import { tools } from '@/data/tools.js'

const props = defineProps({
  toolId: {
    type: String,
    required: true
  }
})

const router = useRouter()
const toolFrame = ref(null)
const loading = ref(true)
const error = ref(null)
const toolComponent = shallowRef(null)

// 获取工具信息
const toolInfo = computed(() => {
  return tools.find(tool => tool.id === props.toolId)
})

// 动态加载 Vue 组件
const loadToolComponent = async () => {
  if (!toolInfo.value?.component) {
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = null

    const componentName = toolInfo.value.component
    let componentModule

    // 根据组件名称动态导入
    switch (componentName) {
      case 'JsonFormatter':
        componentModule = await import('@/components/tools/development/JsonFormatter.vue')
        break
      case 'UuidGenerator':
        componentModule = await import('@/components/tools/development/UuidGenerator.vue')
        break
      case 'Base64Encoder':
        componentModule = await import('@/components/tools/encoding/Base64Encoder.vue')
        break
      case 'UrlEncoder':
        componentModule = await import('@/components/tools/encoding/UrlEncoder.vue')
        break
      case 'Md5Hash':
        componentModule = await import('@/components/tools/security/Md5Hash.vue')
        break
      case 'TimestampConverter':
        componentModule = await import('@/components/tools/conversion/TimestampConverter.vue')
        break
      case 'BaseConverter':
        componentModule = await import('@/components/tools/conversion/BaseConverter.vue')
        break
      case 'CaseConverter':
        componentModule = await import('@/components/tools/text/CaseConverter.vue')
        break
      case 'WordCounter':
        componentModule = await import('@/components/tools/text/WordCounter.vue')
        break
      case 'QrGenerator':
        componentModule = await import('@/components/tools/image/QrGenerator.vue')
        break
      default:
        throw new Error(`未知的组件: ${componentName}`)
    }

    toolComponent.value = componentModule.default
    loading.value = false
  } catch (err) {
    console.error('加载组件失败:', err)
    error.value = `组件加载失败: ${err.message}`
    loading.value = false
  }
}

// 返回主页
const goBack = () => {
  router.push('/')
}

// 刷新工具
const refreshTool = () => {
  if (toolInfo.value?.component) {
    // Vue 组件刷新
    loadToolComponent()
  } else if (toolFrame.value) {
    // iframe 刷新
    loading.value = true
    error.value = null
    toolFrame.value.src = toolFrame.value.src
  }
}

// 在新标签页打开
const openInNewTab = () => {
  if (toolInfo.value?.path) {
    const url = `${window.location.origin}${toolInfo.value.path}`
    window.open(url, '_blank')
  } else if (toolInfo.value?.url) {
    window.open(toolInfo.value.url, '_blank')
  }
}

// 重试加载
const retryLoad = () => {
  error.value = null
  if (toolInfo.value?.component) {
    loadToolComponent()
  } else if (toolFrame.value && toolInfo.value?.url) {
    loading.value = true
    toolFrame.value.src = toolInfo.value.url
  }
}

// iframe加载完成
const onToolLoad = () => {
  loading.value = false
  error.value = null
  setupToolCommunication()
}

// iframe加载错误
const onToolError = () => {
  loading.value = false
  error.value = '工具加载失败，请检查网络连接或稍后重试'
}

// 设置与HTML工具的通信
const setupToolCommunication = () => {
  window.addEventListener('message', handleToolMessage)
  
  // 向工具发送初始化数据
  const initData = {
    type: 'TOOL_INIT',
    theme: document.documentElement.getAttribute('data-theme'),
    toolId: props.toolId
  }
  
  setTimeout(() => {
    toolFrame.value?.contentWindow?.postMessage(initData, '*')
  }, 100)
}

// 处理来自HTML工具的消息
const handleToolMessage = (event) => {
  if (event.origin !== window.location.origin) return
  
  const { type, data } = event.data
  
  switch (type) {
    case 'TOOL_READY':
      console.log('Tool is ready:', data)
      break
    case 'TOOL_ERROR':
      error.value = data.message
      break
    case 'TOOL_RESIZE':
      // 处理工具尺寸变化
      if (data.height) {
        toolFrame.value.style.height = `${data.height}px`
      }
      break
  }
}

// 组件挂载时检查工具是否存在并加载
onMounted(() => {
  if (!toolInfo.value) {
    error.value = '工具不存在'
    loading.value = false
    return
  }

  // 如果有 Vue 组件，加载组件；否则使用 iframe
  if (toolInfo.value.component) {
    loadToolComponent()
  } else {
    loading.value = false
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('message', handleToolMessage)
})
</script>

<style scoped>
.tool-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-color, #ffffff);
}

.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: var(--header-bg, #ffffff);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tool-header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--button-bg, #f3f4f6);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  color: var(--text-color, #374151);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background: var(--button-hover-bg, #e5e7eb);
}

.back-button svg {
  width: 1rem;
  height: 1rem;
}

.tool-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #111827);
  margin: 0;
}

.tool-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--button-bg, #f3f4f6);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  color: var(--text-color, #374151);
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: var(--button-hover-bg, #e5e7eb);
}

.action-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

.tool-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.tool-component {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.tool-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-color, #ffffff);
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color, #e5e7eb);
  border-top: 3px solid var(--primary-color, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
  color: var(--text-color, #374151);
}

.error-message svg {
  width: 3rem;
  height: 3rem;
  color: var(--error-color, #ef4444);
}

.retry-button {
  padding: 0.5rem 1rem;
  background: var(--primary-color, #3b82f6);
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: var(--primary-hover-color, #2563eb);
}

/* 深色主题支持 */
[data-theme="dark"] .tool-container {
  --bg-color: #111827;
  --header-bg: #1f2937;
  --text-color: #f9fafb;
  --border-color: #374151;
  --button-bg: #374151;
  --button-hover-bg: #4b5563;
  --primary-color: #3b82f6;
  --primary-hover-color: #2563eb;
  --error-color: #ef4444;
}
</style>