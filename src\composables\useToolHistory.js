import { ref, computed } from 'vue'

const STORAGE_KEY = 'toolhub_history'
const MAX_HISTORY_ITEMS = 100

export function useToolHistory() {
  const history = ref([])
  const isLoading = ref(false)

  /**
   * 加载历史记录
   */
  const loadHistory = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        history.value = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('加载历史记录失败:', error)
      history.value = []
    }
  }

  /**
   * 保存历史记录
   */
  const saveHistory = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history.value))
    } catch (error) {
      console.warn('保存历史记录失败:', error)
    }
  }

  /**
   * 添加历史记录
   * @param {Object} item - 历史记录项
   * @param {string} item.toolId - 工具ID
   * @param {string} item.toolName - 工具名称
   * @param {string} item.input - 输入内容
   * @param {string} item.output - 输出内容
   * @param {number} item.timestamp - 时间戳
   * @param {Object} item.metadata - 额外元数据
   */
  const addToHistory = (item) => {
    const historyItem = {
      id: Date.now() + Math.random().toString(36).substr(2, 9),
      toolId: item.toolId,
      toolName: item.toolName,
      input: item.input,
      output: item.output,
      timestamp: item.timestamp || Date.now(),
      metadata: item.metadata || {}
    }

    // 添加到历史记录开头
    history.value.unshift(historyItem)

    // 限制历史记录数量
    if (history.value.length > MAX_HISTORY_ITEMS) {
      history.value = history.value.slice(0, MAX_HISTORY_ITEMS)
    }

    saveHistory()
  }

  /**
   * 删除历史记录项
   * @param {string} id - 记录ID
   */
  const removeFromHistory = (id) => {
    const index = history.value.findIndex(item => item.id === id)
    if (index !== -1) {
      history.value.splice(index, 1)
      saveHistory()
    }
  }

  /**
   * 清空历史记录
   */
  const clearHistory = () => {
    history.value = []
    saveHistory()
  }

  /**
   * 根据工具ID获取历史记录
   * @param {string} toolId - 工具ID
   * @returns {Array} 历史记录列表
   */
  const getHistoryByTool = (toolId) => {
    return history.value.filter(item => item.toolId === toolId)
  }

  /**
   * 搜索历史记录
   * @param {string} query - 搜索关键词
   * @returns {Array} 匹配的历史记录
   */
  const searchHistory = (query) => {
    if (!query.trim()) return history.value

    const lowerQuery = query.toLowerCase()
    return history.value.filter(item => 
      item.toolName.toLowerCase().includes(lowerQuery) ||
      item.input.toLowerCase().includes(lowerQuery) ||
      item.output.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * 获取最近使用的工具
   * @param {number} limit - 限制数量
   * @returns {Array} 最近使用的工具列表
   */
  const getRecentTools = (limit = 10) => {
    const toolMap = new Map()
    
    for (const item of history.value) {
      if (!toolMap.has(item.toolId)) {
        toolMap.set(item.toolId, {
          toolId: item.toolId,
          toolName: item.toolName,
          lastUsed: item.timestamp,
          usageCount: 1
        })
      } else {
        const tool = toolMap.get(item.toolId)
        tool.usageCount++
        if (item.timestamp > tool.lastUsed) {
          tool.lastUsed = item.timestamp
        }
      }
    }

    return Array.from(toolMap.values())
      .sort((a, b) => b.lastUsed - a.lastUsed)
      .slice(0, limit)
  }

  /**
   * 获取使用统计
   * @returns {Object} 统计信息
   */
  const getUsageStats = () => {
    const toolStats = new Map()
    let totalUsage = 0

    for (const item of history.value) {
      totalUsage++
      if (toolStats.has(item.toolId)) {
        toolStats.get(item.toolId).count++
      } else {
        toolStats.set(item.toolId, {
          toolId: item.toolId,
          toolName: item.toolName,
          count: 1
        })
      }
    }

    const sortedTools = Array.from(toolStats.values())
      .sort((a, b) => b.count - a.count)

    return {
      totalUsage,
      uniqueTools: toolStats.size,
      mostUsedTools: sortedTools.slice(0, 5),
      allToolStats: sortedTools
    }
  }

  /**
   * 导出历史记录
   * @param {string} format - 导出格式: 'json' | 'csv'
   * @returns {string} 导出内容
   */
  const exportHistory = (format = 'json') => {
    if (format === 'json') {
      return JSON.stringify(history.value, null, 2)
    } else if (format === 'csv') {
      const headers = ['时间', '工具名称', '输入', '输出']
      const rows = history.value.map(item => [
        new Date(item.timestamp).toLocaleString(),
        item.toolName,
        item.input.replace(/"/g, '""'),
        item.output.replace(/"/g, '""')
      ])
      
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n')
      
      return csvContent
    }
    
    throw new Error('不支持的导出格式')
  }

  /**
   * 导入历史记录
   * @param {string} content - 导入内容
   * @param {string} format - 导入格式
   */
  const importHistory = (content, format = 'json') => {
    try {
      if (format === 'json') {
        const importedHistory = JSON.parse(content)
        if (Array.isArray(importedHistory)) {
          // 合并历史记录，去重
          const existingIds = new Set(history.value.map(item => item.id))
          const newItems = importedHistory.filter(item => !existingIds.has(item.id))
          
          history.value = [...newItems, ...history.value]
          
          // 限制数量
          if (history.value.length > MAX_HISTORY_ITEMS) {
            history.value = history.value.slice(0, MAX_HISTORY_ITEMS)
          }
          
          saveHistory()
        }
      }
    } catch (error) {
      throw new Error('导入历史记录失败: ' + error.message)
    }
  }

  // 计算属性
  const historyCount = computed(() => history.value.length)
  const isEmpty = computed(() => history.value.length === 0)

  // 初始化时加载历史记录
  loadHistory()

  return {
    history,
    historyCount,
    isEmpty,
    isLoading,
    addToHistory,
    removeFromHistory,
    clearHistory,
    getHistoryByTool,
    searchHistory,
    getRecentTools,
    getUsageStats,
    exportHistory,
    importHistory,
    loadHistory,
    saveHistory
  }
}
