<template>
  <ToolLayout
    title="MD5哈希工具"
    description="MD5、SHA1、SHA256、SHA512等哈希加密工具"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📝</span>
          输入文本
        </h2>
        
        <!-- 哈希算法选择 -->
        <div class="hash-type-selector">
          <label class="block text-sm font-medium mb-2">哈希算法</label>
          <div class="hash-options">
            <label
              v-for="type in hashTypes"
              :key="type.value"
              class="hash-option"
              :class="{ active: selectedHashType === type.value }"
            >
              <input
                v-model="selectedHashType"
                type="radio"
                :value="type.value"
                class="hidden"
                @change="handleHashTypeChange"
              />
              <span class="option-text">{{ type.label }}</span>
            </label>
          </div>
        </div>
        
        <textarea 
          v-model="inputText"
          class="text-input"
          placeholder="请输入要加密的文本..."
          @input="handleInput"
        ></textarea>
        
        <!-- 文件上传 -->
        <div class="file-upload-section">
          <FileUpload
            :accept="'.txt,.json,.xml,.csv'"
            :max-size="10 * 1024 * 1024"
            accept-text="支持文本文件 (最大10MB)"
            @files-processed="handleFileUpload"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button 
            @click="generateHash"
            class="btn-primary"
            :disabled="!inputText.trim()"
          >
            <span class="btn-icon">🔐</span>
            生成哈希
          </button>
          
          <button 
            @click="clearAll"
            class="btn-secondary"
          >
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
      </div>
      
      <!-- 输出区域 -->
      <div class="output-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">🔐</span>
          {{ selectedHashType.toUpperCase() }} 哈希值
        </h2>
        
        <div class="hash-result-container">
          <div v-if="!hashResult && !isProcessing" class="empty-state">
            等待输入文本...
          </div>
          
          <div v-else-if="isProcessing" class="loading-state">
            <div class="loading-spinner">⏳</div>
            <p>计算中...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">❌</div>
            <p>{{ error }}</p>
          </div>
          
          <div v-else class="hash-result">
            {{ hashResult }}
          </div>
          
          <!-- 复制按钮 -->
          <button
            v-if="hashResult"
            @click="copyHash"
            class="copy-btn"
            :class="{ copied: copySuccess }"
          >
            {{ copySuccess ? '✅ 已复制' : '📋 复制' }}
          </button>
        </div>
        
        <!-- 哈希信息 -->
        <div v-if="hashResult" class="hash-info">
          <div class="info-item">
            <span class="info-label">哈希长度：</span>
            <span class="info-value">{{ hashResult.length }} 字符</span>
          </div>
          <div class="info-item">
            <span class="info-label">输入长度：</span>
            <span class="info-value">{{ inputText.length }} 字符</span>
          </div>
          <div class="info-item">
            <span class="info-label">处理时间：</span>
            <span class="info-value">{{ processTime }} ms</span>
          </div>
          <div class="info-item">
            <span class="info-label">算法类型：</span>
            <span class="info-value">{{ selectedHashType.toUpperCase() }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能说明 -->
    <div class="mt-8 info-section sketch-border">
      <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
        <span class="mr-2">💡</span>
        哈希加密说明
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
          <h4 class="font-medium text-gray-800 mb-2">🔐 MD5算法</h4>
          <p>128位哈希值，常用于文件校验和数据完整性验证</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">🛡️ SHA系列</h4>
          <p>更安全的哈希算法，SHA256和SHA512提供更高安全性</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">📁 文件支持</h4>
          <p>支持上传文本文件进行哈希计算</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">🔒 本地处理</h4>
          <p>所有计算在本地完成，保护您的数据安全</p>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import CryptoJS from 'crypto-js'
import ToolLayout from '@/components/common/ToolLayout.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const selectedHashType = ref('md5')
const inputText = ref('')
const hashResult = ref('')
const error = ref('')
const isProcessing = ref(false)
const processTime = ref(0)
const copySuccess = ref(false)

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 哈希算法类型
const hashTypes = [
  { value: 'md5', label: 'MD5' },
  { value: 'sha1', label: 'SHA1' },
  { value: 'sha256', label: 'SHA256' },
  { value: 'sha512', label: 'SHA512' }
]

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时生成哈希（防抖）
const handleInput = debounce(() => {
  if (inputText.value.trim()) {
    generateHash(true) // 静默生成
  }
}, 500)

// 哈希类型变化处理
const handleHashTypeChange = () => {
  if (inputText.value.trim()) {
    generateHash()
  }
}

// 生成哈希
const generateHash = (silent = false) => {
  if (!inputText.value.trim()) {
    if (!silent) error.value = '请输入要加密的文本'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  const startTime = performance.now()
  
  try {
    let hash
    
    switch (selectedHashType.value) {
      case 'md5':
        hash = CryptoJS.MD5(inputText.value).toString()
        break
      case 'sha1':
        hash = CryptoJS.SHA1(inputText.value).toString()
        break
      case 'sha256':
        hash = CryptoJS.SHA256(inputText.value).toString()
        break
      case 'sha512':
        hash = CryptoJS.SHA512(inputText.value).toString()
        break
      default:
        throw new Error('不支持的哈希类型')
    }
    
    const endTime = performance.now()
    processTime.value = (endTime - startTime).toFixed(2)
    
    hashResult.value = hash
    
    // 添加到历史记录
    if (!silent) {
      addToHistory({
        toolId: 'md5-hash',
        toolName: 'MD5哈希工具',
        input: inputText.value,
        output: hash,
        metadata: { 
          algorithm: selectedHashType.value,
          processTime: processTime.value
        }
      })
    }
    
  } catch (err) {
    error.value = `哈希生成失败: ${err.message}`
    hashResult.value = ''
  } finally {
    isProcessing.value = false
  }
}

// 处理文件上传
const handleFileUpload = (files) => {
  if (files.length > 0 && files[0].content) {
    inputText.value = files[0].content
    generateHash()
  }
}

// 复制哈希值
const copyHash = async () => {
  if (!hashResult.value) return
  
  const result = await copyToClipboard(hashResult.value)
  if (result.success) {
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  }
}

// 清空所有内容
const clearAll = () => {
  inputText.value = ''
  hashResult.value = ''
  error.value = ''
  processTime.value = 0
}

// 加载示例
const loadExample = () => {
  inputText.value = 'Hello World!'
  generateHash()
}

// 组件挂载时加载示例
onMounted(() => {
  loadExample()
})
</script>

<style scoped>
.input-section,
.output-section,
.info-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #a855f7;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #faf5ff, #f3e8ff);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #c084fc;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.hash-type-selector {
  @apply mb-4;
}

.hash-options {
  @apply flex flex-wrap gap-2;
}

.hash-option {
  @apply px-3 py-2 border-2 border-purple-300 rounded-lg bg-white text-purple-700 font-medium cursor-pointer transition-all duration-200;
}

.hash-option.active {
  @apply bg-purple-500 text-white border-purple-600;
}

.hash-option:hover {
  @apply transform -translate-y-0.5 shadow-md;
}

.option-text {
  @apply pointer-events-none;
}

.text-input {
  @apply w-full h-32 p-3 border-2 border-purple-300 rounded-lg text-sm resize-none;
  @apply focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200;
  background: white;
  transition: border-color 0.3s ease;
}

.file-upload-section {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-purple-500 text-white border-purple-600 hover:bg-purple-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

.hash-result-container {
  @apply relative bg-gray-900 rounded-lg p-4 min-h-32;
}

.empty-state,
.loading-state,
.error-state {
  @apply flex flex-col items-center justify-center h-24 text-gray-400;
}

.loading-state {
  @apply text-green-400;
}

.loading-spinner {
  @apply text-2xl mb-2;
  animation: spin 1s linear infinite;
}

.error-state {
  @apply text-red-400;
}

.error-icon {
  @apply text-2xl mb-2;
}

.hash-result {
  @apply font-mono text-sm text-green-400 break-all leading-relaxed;
}

.copy-btn {
  @apply absolute top-2 right-2 bg-purple-500 text-white px-3 py-1 rounded text-xs transition-all duration-200;
}

.copy-btn:hover {
  @apply bg-purple-600 transform scale-105;
}

.copy-btn.copied {
  @apply bg-green-500;
}

.hash-info {
  @apply mt-4 space-y-2 text-sm;
}

.info-item {
  @apply flex justify-between;
}

.info-label {
  @apply text-gray-600;
}

.info-value {
  @apply font-mono text-gray-800;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
  
  .hash-options {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>
