<template>
  <div class="result-display">
    <!-- 结果头部 -->
    <div class="result-header">
      <h3 class="result-title">
        <span class="title-icon">📋</span>
        {{ title || '处理结果' }}
      </h3>
      <div class="result-actions">
        <button
          v-if="allowCopy && content"
          @click="copyContent"
          class="btn-copy"
          :class="{ 'copied': copySuccess }"
        >
          <span v-if="!copySuccess">📋 复制</span>
          <span v-else>✅ 已复制</span>
        </button>
        <button
          v-if="allowDownload && content"
          @click="downloadContent"
          class="btn-download"
        >
          💾 下载
        </button>
        <button
          v-if="allowClear"
          @click="clearContent"
          class="btn-clear"
        >
          🗑️ 清空
        </button>
      </div>
    </div>

    <!-- 结果内容 -->
    <div class="result-content sketch-border">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">⏳</div>
        <p>处理中...</p>
      </div>
      
      <div v-else-if="error" class="error-state">
        <div class="error-icon">❌</div>
        <p class="error-message">{{ error }}</p>
      </div>
      
      <div v-else-if="content" class="content-area">
        <!-- 文本内容 -->
        <pre v-if="type === 'text' || type === 'json'" class="text-content">{{ content }}</pre>
        
        <!-- HTML内容 -->
        <div v-else-if="type === 'html'" class="html-content" v-html="content"></div>
        
        <!-- 图片内容 -->
        <div v-else-if="type === 'image'" class="image-content">
          <img :src="content" :alt="title" class="result-image" />
        </div>
        
        <!-- 表格内容 -->
        <div v-else-if="type === 'table'" class="table-content">
          <table class="result-table">
            <thead v-if="content.headers">
              <tr>
                <th v-for="header in content.headers" :key="header">{{ header }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in content.rows" :key="index">
                <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 默认内容 -->
        <div v-else class="default-content">
          {{ content }}
        </div>
      </div>
      
      <div v-else class="empty-state">
        <div class="empty-icon">📄</div>
        <p>暂无结果</p>
      </div>
    </div>

    <!-- 结果统计信息 -->
    <div v-if="stats" class="result-stats">
      <div class="stat-item" v-for="(value, key) in stats" :key="key">
        <span class="stat-label">{{ key }}:</span>
        <span class="stat-value">{{ value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useClipboard } from '@/composables/useClipboard'

const props = defineProps({
  content: {
    type: [String, Object, Array],
    default: ''
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => ['text', 'json', 'html', 'image', 'table'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  allowCopy: {
    type: Boolean,
    default: true
  },
  allowDownload: {
    type: Boolean,
    default: false
  },
  allowClear: {
    type: Boolean,
    default: false
  },
  downloadFilename: {
    type: String,
    default: 'result.txt'
  },
  stats: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['clear'])

const { copyToClipboard } = useClipboard()
const copySuccess = ref(false)

const copyContent = async () => {
  const textContent = typeof props.content === 'string' 
    ? props.content 
    : JSON.stringify(props.content, null, 2)
  
  const result = await copyToClipboard(textContent)
  if (result.success) {
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  }
}

const downloadContent = () => {
  const textContent = typeof props.content === 'string' 
    ? props.content 
    : JSON.stringify(props.content, null, 2)
  
  const blob = new Blob([textContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = props.downloadFilename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const clearContent = () => {
  emit('clear')
}
</script>

<style scoped>
.result-display {
  @apply space-y-4;
}

.result-header {
  @apply flex justify-between items-center;
}

.result-title {
  @apply text-lg font-semibold text-gray-800 flex items-center gap-2;
}

.title-icon {
  @apply text-xl;
}

.result-actions {
  @apply flex gap-2;
}

.btn-copy,
.btn-download,
.btn-clear {
  @apply px-3 py-1 text-sm rounded-lg border-2 transition-all duration-200;
}

.btn-copy {
  @apply bg-blue-500 text-white border-blue-600 hover:bg-blue-600;
}

.btn-copy.copied {
  @apply bg-green-500 border-green-600;
}

.btn-download {
  @apply bg-purple-500 text-white border-purple-600 hover:bg-purple-600;
}

.btn-clear {
  @apply bg-red-500 text-white border-red-600 hover:bg-red-600;
}

.result-content {
  @apply bg-white p-4 min-h-32;
  background: linear-gradient(145deg, #f8fafc, #f1f5f9);
}

.sketch-border {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  position: relative;
}

.loading-state,
.error-state,
.empty-state {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

.loading-spinner {
  @apply text-2xl mb-2;
  animation: spin 1s linear infinite;
}

.error-state {
  @apply text-red-500;
}

.error-icon {
  @apply text-2xl mb-2;
}

.empty-icon {
  @apply text-2xl mb-2;
}

.content-area {
  @apply w-full;
}

.text-content {
  @apply whitespace-pre-wrap font-mono text-sm bg-gray-50 p-3 rounded border overflow-auto max-h-96;
}

.html-content {
  @apply prose max-w-none;
}

.image-content {
  @apply text-center;
}

.result-image {
  @apply max-w-full h-auto rounded border;
}

.table-content {
  @apply overflow-auto;
}

.result-table {
  @apply w-full border-collapse border border-gray-300;
}

.result-table th,
.result-table td {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.result-table th {
  @apply bg-gray-100 font-semibold;
}

.result-stats {
  @apply flex flex-wrap gap-4 p-3 bg-gray-50 rounded border;
}

.stat-item {
  @apply flex gap-1;
}

.stat-label {
  @apply font-medium text-gray-600;
}

.stat-value {
  @apply text-gray-800;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    @apply flex-col space-y-2 items-start;
  }
  
  .result-actions {
    @apply w-full justify-start;
  }
}
</style>
