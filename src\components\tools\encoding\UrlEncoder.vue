<template>
  <ToolLayout
    title="URL编解码器"
    description="URL编码解码和分析工具，支持完整URL和组件编码"
  >
    <div class="space-y-6">
      <!-- 模式切换 -->
      <div class="mode-tabs sketch-border">
        <div class="tab-buttons">
          <button
            @click="currentMode = 'encode'"
            class="tab-button"
            :class="{ active: currentMode === 'encode' }"
          >
            <span class="tab-icon">🔐</span>
            编码
          </button>
          <button
            @click="currentMode = 'decode'"
            class="tab-button"
            :class="{ active: currentMode === 'decode' }"
          >
            <span class="tab-icon">🔓</span>
            解码
          </button>
          <button
            @click="currentMode = 'analyze'"
            class="tab-button"
            :class="{ active: currentMode === 'analyze' }"
          >
            <span class="tab-icon">🔍</span>
            分析
          </button>
        </div>
      </div>

      <!-- 编码模式 -->
      <div v-if="currentMode === 'encode'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 输入区域 -->
        <div class="input-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">🌐</span>
            原始URL
          </h2>
          
          <!-- 编码类型选择 -->
          <div class="encode-options">
            <label class="option-label">
              <input
                v-model="encodeType"
                type="radio"
                value="component"
                class="option-radio"
              />
              <span>组件编码（推荐）</span>
            </label>
            <label class="option-label">
              <input
                v-model="encodeType"
                type="radio"
                value="full"
                class="option-radio"
              />
              <span>完整编码</span>
            </label>
          </div>
          
          <textarea 
            v-model="encodeInput"
            class="text-input"
            placeholder="请输入要编码的URL..."
            @input="handleEncodeInput"
          ></textarea>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button 
              @click="encodeUrl"
              class="btn-primary"
              :disabled="!encodeInput.trim()"
            >
              <span class="btn-icon">🔐</span>
              编码
            </button>
            
            <button 
              @click="clearEncode"
              class="btn-secondary"
            >
              <span class="btn-icon">🗑️</span>
              清空
            </button>
          </div>
        </div>
        
        <!-- 输出区域 -->
        <div class="output-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📋</span>
            编码结果
          </h2>
          
          <ResultDisplay
            :content="encodeOutput"
            :type="'text'"
            :loading="isProcessing"
            :error="error"
            :allow-copy="true"
            :allow-download="true"
            :download-filename="'url_encoded.txt'"
            :stats="encodeStats"
            @clear="clearEncodeOutput"
          />
        </div>
      </div>

      <!-- 解码模式 -->
      <div v-if="currentMode === 'decode'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 输入区域 -->
        <div class="input-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">🔐</span>
            编码的URL
          </h2>
          
          <textarea 
            v-model="decodeInput"
            class="text-input"
            placeholder="请输入要解码的URL..."
            @input="handleDecodeInput"
          ></textarea>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button 
              @click="decodeUrl"
              class="btn-primary"
              :disabled="!decodeInput.trim()"
            >
              <span class="btn-icon">🔓</span>
              解码
            </button>
            
            <button 
              @click="clearDecode"
              class="btn-secondary"
            >
              <span class="btn-icon">🗑️</span>
              清空
            </button>
          </div>
        </div>
        
        <!-- 输出区域 -->
        <div class="output-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📋</span>
            解码结果
          </h2>
          
          <ResultDisplay
            :content="decodeOutput"
            :type="'text'"
            :loading="isProcessing"
            :error="error"
            :allow-copy="true"
            :allow-download="true"
            :download-filename="'url_decoded.txt'"
            :stats="decodeStats"
            @clear="clearDecodeOutput"
          />
        </div>
      </div>

      <!-- 分析模式 -->
      <div v-if="currentMode === 'analyze'" class="space-y-6">
        <!-- 输入区域 -->
        <div class="input-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">🔍</span>
            URL分析
          </h2>
          
          <textarea 
            v-model="analyzeInput"
            class="text-input"
            placeholder="请输入要分析的URL..."
            @input="handleAnalyzeInput"
          ></textarea>
          
          <div class="action-buttons">
            <button 
              @click="analyzeUrl"
              class="btn-primary"
              :disabled="!analyzeInput.trim()"
            >
              <span class="btn-icon">🔍</span>
              分析
            </button>
            
            <button 
              @click="clearAnalyze"
              class="btn-secondary"
            >
              <span class="btn-icon">🗑️</span>
              清空
            </button>
          </div>
        </div>
        
        <!-- 分析结果 -->
        <div v-if="analyzeResult" class="analysis-result sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📊</span>
            分析结果
          </h2>
          
          <ResultDisplay
            :content="analyzeResult"
            :type="'table'"
            :allow-copy="true"
            :allow-download="true"
            :download-filename="'url_analysis.json'"
            @clear="clearAnalyzeResult"
          />
        </div>
      </div>
      
      <!-- 功能说明 -->
      <div class="info-section sketch-border">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <span class="mr-2">💡</span>
          URL编码说明
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔐 编码功能</h4>
            <p>将URL中的特殊字符转换为百分号编码格式，确保URL传输安全</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔓 解码功能</h4>
            <p>将百分号编码的URL还原为可读的原始格式</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔍 分析功能</h4>
            <p>解析URL的各个组成部分，包括协议、域名、路径、参数等</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🌍 多语言支持</h4>
            <p>支持中文、特殊字符等各种URL编码需求</p>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const currentMode = ref('encode')
const encodeType = ref('component')
const encodeInput = ref('')
const encodeOutput = ref('')
const decodeInput = ref('')
const decodeOutput = ref('')
const analyzeInput = ref('')
const analyzeResult = ref(null)
const error = ref('')
const isProcessing = ref(false)

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 计算属性
const encodeStats = computed(() => {
  if (!encodeOutput.value) return null
  
  const inputLength = encodeInput.value.length
  const outputLength = encodeOutput.value.length
  const expansion = inputLength > 0 ? ((outputLength - inputLength) / inputLength * 100).toFixed(1) : 0
  
  return {
    '原始长度': `${inputLength} 字符`,
    '编码长度': `${outputLength} 字符`,
    '长度增长': `${expansion}%`
  }
})

const decodeStats = computed(() => {
  if (!decodeOutput.value) return null
  
  const inputLength = decodeInput.value.length
  const outputLength = decodeOutput.value.length
  const compression = inputLength > 0 ? ((inputLength - outputLength) / inputLength * 100).toFixed(1) : 0
  
  return {
    '编码长度': `${inputLength} 字符`,
    '解码长度': `${outputLength} 字符`,
    '长度减少': `${compression}%`
  }
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时编码（防抖）
const handleEncodeInput = debounce(() => {
  if (encodeInput.value.trim()) {
    encodeUrl()
  }
}, 500)

// 实时解码（防抖）
const handleDecodeInput = debounce(() => {
  if (decodeInput.value.trim()) {
    decodeUrl()
  }
}, 500)

// 实时分析（防抖）
const handleAnalyzeInput = debounce(() => {
  if (analyzeInput.value.trim()) {
    analyzeUrl()
  }
}, 500)

// URL编码
const encodeUrl = () => {
  if (!encodeInput.value.trim()) {
    error.value = '请输入要编码的URL'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    let encoded
    
    if (encodeType.value === 'component') {
      // 只编码URL组件（查询参数、路径等）
      encoded = encodeInput.value.replace(/[^\w\-\.~:/?#\[\]@!$&'()*+,;=]/g, function(char) {
        return encodeURIComponent(char)
      })
    } else {
      // 完整URL编码
      encoded = encodeURIComponent(encodeInput.value)
    }
    
    encodeOutput.value = encoded
    
    // 添加到历史记录
    addToHistory({
      toolId: 'url-encoder',
      toolName: 'URL编解码器',
      input: encodeInput.value,
      output: encoded,
      metadata: { action: 'encode', type: encodeType.value }
    })
    
  } catch (err) {
    error.value = `编码失败: ${err.message}`
    encodeOutput.value = ''
  } finally {
    isProcessing.value = false
  }
}

// URL解码
const decodeUrl = () => {
  if (!decodeInput.value.trim()) {
    error.value = '请输入要解码的URL'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    const decoded = decodeURIComponent(decodeInput.value)
    decodeOutput.value = decoded
    
    // 添加到历史记录
    addToHistory({
      toolId: 'url-encoder',
      toolName: 'URL编解码器',
      input: decodeInput.value,
      output: decoded,
      metadata: { action: 'decode' }
    })
    
  } catch (err) {
    error.value = `解码失败: ${err.message}`
    decodeOutput.value = ''
  } finally {
    isProcessing.value = false
  }
}

// URL分析
const analyzeUrl = () => {
  if (!analyzeInput.value.trim()) {
    error.value = '请输入要分析的URL'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    const url = new URL(analyzeInput.value)
    
    const analysis = {
      headers: ['组件', '值'],
      rows: [
        ['完整URL', analyzeInput.value],
        ['协议', url.protocol],
        ['主机名', url.hostname],
        ['端口', url.port || '默认'],
        ['路径', url.pathname],
        ['查询字符串', url.search],
        ['锚点', url.hash],
        ['源', url.origin]
      ]
    }
    
    // 解析查询参数
    if (url.searchParams.size > 0) {
      analysis.rows.push(['', ''])
      analysis.rows.push(['查询参数', ''])
      for (const [key, value] of url.searchParams) {
        analysis.rows.push([`  ${key}`, value])
      }
    }
    
    analyzeResult.value = analysis
    
    // 添加到历史记录
    addToHistory({
      toolId: 'url-encoder',
      toolName: 'URL编解码器',
      input: analyzeInput.value,
      output: JSON.stringify(analysis, null, 2),
      metadata: { action: 'analyze' }
    })
    
  } catch (err) {
    error.value = `分析失败: ${err.message}`
    analyzeResult.value = null
  } finally {
    isProcessing.value = false
  }
}

// 清空函数
const clearEncode = () => {
  encodeInput.value = ''
  encodeOutput.value = ''
  error.value = ''
}

const clearDecode = () => {
  decodeInput.value = ''
  decodeOutput.value = ''
  error.value = ''
}

const clearAnalyze = () => {
  analyzeInput.value = ''
  analyzeResult.value = null
  error.value = ''
}

const clearEncodeOutput = () => {
  encodeOutput.value = ''
  error.value = ''
}

const clearDecodeOutput = () => {
  decodeOutput.value = ''
  error.value = ''
}

const clearAnalyzeResult = () => {
  analyzeResult.value = null
  error.value = ''
}

// 加载示例
const loadExample = () => {
  encodeInput.value = 'https://example.com/搜索?查询=你好世界&类型=文档#章节'
  encodeUrl()
}

// 组件挂载时加载示例
onMounted(() => {
  loadExample()
})
</script>

<style scoped>
.mode-tabs {
  @apply bg-white p-4;
}

.tab-buttons {
  @apply flex gap-2;
}

.tab-button {
  @apply px-4 py-2 border-2 border-green-300 rounded-lg bg-white text-green-700 font-medium transition-all duration-200 cursor-pointer;
}

.tab-button.active {
  @apply bg-green-500 text-white border-green-600;
}

.tab-button:hover {
  @apply transform -translate-y-0.5 shadow-md;
}

.tab-icon {
  @apply mr-2;
}

.input-section,
.output-section,
.analysis-result,
.info-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #10b981;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #f0fdf4, #ecfdf5);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #34d399;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.encode-options {
  @apply flex gap-4 mb-4;
}

.option-label {
  @apply flex items-center gap-2 cursor-pointer;
}

.option-radio {
  @apply text-green-500 focus:ring-green-500;
}

.text-input {
  @apply w-full h-32 p-3 border-2 border-green-300 rounded-lg text-sm resize-none;
  @apply focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200;
  background: white;
  transition: border-color 0.3s ease;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-green-500 text-white border-green-600 hover:bg-green-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
  
  .encode-options {
    @apply flex-col gap-2;
  }
}
</style>
