<template>
  <ToolLayout
    title="大小写转换器"
    description="文本大小写格式转换工具，支持多种命名规范"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📝</span>
          输入文本
        </h2>
        
        <textarea 
          v-model="inputText"
          class="text-input"
          placeholder="请输入要转换的文本..."
          @input="handleInput"
        ></textarea>
        
        <!-- 文件上传 -->
        <div class="file-upload-section">
          <FileUpload
            :accept="'.txt'"
            :max-size="1024 * 1024"
            accept-text="支持文本文件"
            @files-processed="handleFileUpload"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button 
            @click="convertAllCases"
            class="btn-primary"
            :disabled="!inputText.trim()"
          >
            <span class="btn-icon">🔄</span>
            转换
          </button>
          
          <button 
            @click="clearAll"
            class="btn-secondary"
          >
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
        
        <!-- 文本统计 -->
        <div v-if="inputText" class="text-stats">
          <div class="stat-item">
            <span class="stat-label">字符数:</span>
            <span class="stat-value">{{ textStats.characters }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">单词数:</span>
            <span class="stat-value">{{ textStats.words }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">行数:</span>
            <span class="stat-value">{{ textStats.lines }}</span>
          </div>
        </div>
      </div>
      
      <!-- 输出区域 -->
      <div class="output-section">
        <!-- 基础转换 -->
        <div class="conversion-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">🔤</span>
            基础转换
          </h3>
          
          <div class="conversion-cards">
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">全部大写 (UPPERCASE)</span>
                <button @click="copyResult(results.uppercase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.uppercase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">全部小写 (lowercase)</span>
                <button @click="copyResult(results.lowercase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.lowercase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">首字母大写 (Title Case)</span>
                <button @click="copyResult(results.titleCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.titleCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">句首大写 (Sentence case)</span>
                <button @click="copyResult(results.sentenceCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.sentenceCase || '在左侧输入文本...' }}</div>
            </div>
          </div>
        </div>
        
        <!-- 编程命名转换 -->
        <div class="conversion-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">💻</span>
            编程命名转换
          </h3>
          
          <div class="conversion-cards">
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">驼峰命名 (camelCase)</span>
                <button @click="copyResult(results.camelCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.camelCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">帕斯卡命名 (PascalCase)</span>
                <button @click="copyResult(results.pascalCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.pascalCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">下划线命名 (snake_case)</span>
                <button @click="copyResult(results.snakeCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.snakeCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">短横线命名 (kebab-case)</span>
                <button @click="copyResult(results.kebabCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.kebabCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">常量命名 (CONSTANT_CASE)</span>
                <button @click="copyResult(results.constantCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.constantCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">点分命名 (dot.case)</span>
                <button @click="copyResult(results.dotCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.dotCase || '在左侧输入文本...' }}</div>
            </div>
          </div>
        </div>
        
        <!-- 特殊转换 -->
        <div class="conversion-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">✨</span>
            特殊转换
          </h3>
          
          <div class="conversion-cards">
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">反转大小写 (iNVERT cASE)</span>
                <button @click="copyResult(results.invertCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.invertCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">随机大小写 (RaNdOm CaSe)</span>
                <button @click="copyResult(results.randomCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.randomCase || '在左侧输入文本...' }}</div>
            </div>
            
            <div class="conversion-card">
              <div class="card-header">
                <span class="card-label">交替大小写 (AlTeRnAtInG)</span>
                <button @click="copyResult(results.alternatingCase)" class="copy-btn">📋</button>
              </div>
              <div class="card-content">{{ results.alternatingCase || '在左侧输入文本...' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const inputText = ref('')
const results = ref({
  uppercase: '',
  lowercase: '',
  titleCase: '',
  sentenceCase: '',
  camelCase: '',
  pascalCase: '',
  snakeCase: '',
  kebabCase: '',
  constantCase: '',
  dotCase: '',
  invertCase: '',
  randomCase: '',
  alternatingCase: ''
})

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 计算属性 - 文本统计
const textStats = computed(() => {
  if (!inputText.value) {
    return { characters: 0, words: 0, lines: 0 }
  }
  
  const text = inputText.value
  return {
    characters: text.length,
    words: text.trim() ? text.trim().split(/\s+/).length : 0,
    lines: text.split('\n').length
  }
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时转换（防抖）
const handleInput = debounce(() => {
  if (inputText.value.trim()) {
    convertAllCases(true)
  }
}, 500)

// 转换函数
const convertAllCases = (silent = false) => {
  if (!inputText.value.trim()) {
    clearResults()
    return
  }
  
  const text = inputText.value
  
  // 基础转换
  results.value.uppercase = text.toUpperCase()
  results.value.lowercase = text.toLowerCase()
  results.value.titleCase = toTitleCase(text)
  results.value.sentenceCase = toSentenceCase(text)
  
  // 编程命名转换
  results.value.camelCase = toCamelCase(text)
  results.value.pascalCase = toPascalCase(text)
  results.value.snakeCase = toSnakeCase(text)
  results.value.kebabCase = toKebabCase(text)
  results.value.constantCase = toConstantCase(text)
  results.value.dotCase = toDotCase(text)
  
  // 特殊转换
  results.value.invertCase = toInvertCase(text)
  results.value.randomCase = toRandomCase(text)
  results.value.alternatingCase = toAlternatingCase(text)
  
  if (!silent) {
    addToHistory({
      toolId: 'case-converter',
      toolName: '大小写转换器',
      input: text,
      output: `转换为${Object.keys(results.value).length}种格式`,
      metadata: { conversions: Object.keys(results.value).length }
    })
  }
}

// 转换函数实现
const toTitleCase = (text) => {
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  )
}

const toSentenceCase = (text) => {
  return text.replace(/(^\w|[.!?]\s*\w)/g, (match) => match.toUpperCase())
}

const toCamelCase = (text) => {
  return text
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
      index === 0 ? word.toLowerCase() : word.toUpperCase()
    )
    .replace(/\s+/g, '')
    .replace(/[^\w]/g, '')
}

const toPascalCase = (text) => {
  return text
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
    .replace(/\s+/g, '')
    .replace(/[^\w]/g, '')
}

const toSnakeCase = (text) => {
  return text
    .replace(/\W+/g, ' ')
    .split(/ |\B(?=[A-Z])/)
    .map(word => word.toLowerCase())
    .join('_')
}

const toKebabCase = (text) => {
  return text
    .replace(/\W+/g, ' ')
    .split(/ |\B(?=[A-Z])/)
    .map(word => word.toLowerCase())
    .join('-')
}

const toConstantCase = (text) => {
  return toSnakeCase(text).toUpperCase()
}

const toDotCase = (text) => {
  return text
    .replace(/\W+/g, ' ')
    .split(/ |\B(?=[A-Z])/)
    .map(word => word.toLowerCase())
    .join('.')
}

const toInvertCase = (text) => {
  return text
    .split('')
    .map(char => 
      char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase()
    )
    .join('')
}

const toRandomCase = (text) => {
  return text
    .split('')
    .map(char => 
      Math.random() > 0.5 ? char.toUpperCase() : char.toLowerCase()
    )
    .join('')
}

const toAlternatingCase = (text) => {
  let isUpper = false
  return text
    .split('')
    .map(char => {
      if (/[a-zA-Z]/.test(char)) {
        isUpper = !isUpper
        return isUpper ? char.toUpperCase() : char.toLowerCase()
      }
      return char
    })
    .join('')
}

// 处理文件上传
const handleFileUpload = (files) => {
  if (files.length > 0 && files[0].content) {
    inputText.value = files[0].content
    convertAllCases()
  }
}

// 复制结果
const copyResult = (value) => {
  if (value) {
    copyToClipboard(value)
  }
}

// 清空所有
const clearAll = () => {
  inputText.value = ''
  clearResults()
}

// 清空结果
const clearResults = () => {
  Object.keys(results.value).forEach(key => {
    results.value[key] = ''
  })
}
</script>

<style scoped>
.input-section {
  @apply bg-white p-6;
}

.output-section {
  @apply space-y-6;
}

.conversion-group {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title,
.group-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.text-input {
  @apply w-full h-40 p-3 border-2 border-yellow-300 rounded-lg text-sm resize-y;
  @apply focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200;
  background: white;
  transition: border-color 0.3s ease;
}

.file-upload-section {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-yellow-500 text-white border-yellow-600 hover:bg-yellow-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

.text-stats {
  @apply mt-4 flex gap-4 text-sm;
}

.stat-item {
  @apply flex gap-1;
}

.stat-label {
  @apply text-gray-600;
}

.stat-value {
  @apply font-semibold text-gray-800;
}

.conversion-cards {
  @apply space-y-3;
}

.conversion-card {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-4 relative;
}

.card-header {
  @apply flex justify-between items-center mb-2;
}

.card-label {
  @apply text-sm font-medium text-gray-700;
}

.copy-btn {
  @apply bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600 transition-colors;
}

.card-content {
  @apply font-mono text-sm text-gray-800 break-all min-h-8 whitespace-pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
  
  .text-stats {
    @apply flex-col gap-2;
  }
}
</style>
