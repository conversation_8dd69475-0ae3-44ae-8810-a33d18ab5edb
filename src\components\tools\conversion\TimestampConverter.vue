<template>
  <ToolLayout
    title="时间戳转换器"
    description="时间戳与日期时间相互转换工具"
  >
    <div class="space-y-6">
      <!-- 当前时间显示 -->
      <div class="current-time-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⏰</span>
          当前时间
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="time-card">
            <div class="time-label">当前时间戳</div>
            <div class="time-value">{{ currentTimestamp }}</div>
            <button @click="copyTimestamp" class="copy-btn">📋 复制</button>
          </div>
          
          <div class="time-card">
            <div class="time-label">当前日期时间</div>
            <div class="time-value">{{ currentDateTime }}</div>
            <button @click="copyDateTime" class="copy-btn">📋 复制</button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 时间戳转日期 -->
        <div class="converter-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📅</span>
            时间戳转日期
          </h2>
          
          <div class="input-group">
            <label class="input-label">时间戳（秒）</label>
            <div class="input-with-button">
              <input
                v-model="timestampInput"
                type="number"
                class="input-field"
                placeholder="请输入时间戳..."
                @input="handleTimestampInput"
              />
              <button
                @click="useCurrentTimestamp"
                class="btn-helper"
              >
                使用当前
              </button>
            </div>
          </div>
          
          <div class="action-buttons">
            <button
              @click="convertTimestampToDate"
              class="btn-primary"
              :disabled="!timestampInput"
            >
              <span class="btn-icon">🔄</span>
              转换
            </button>
          </div>
          
          <ResultDisplay
            v-if="timestampResult"
            :content="timestampResult"
            :type="'text'"
            :allow-copy="true"
            title="转换结果"
            @clear="clearTimestampResult"
          />
        </div>
        
        <!-- 日期转时间戳 -->
        <div class="converter-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">🕐</span>
            日期转时间戳
          </h2>
          
          <div class="input-group">
            <label class="input-label">日期</label>
            <input
              v-model="dateInput"
              type="date"
              class="input-field"
            />
          </div>
          
          <div class="input-group">
            <label class="input-label">时间</label>
            <input
              v-model="timeInput"
              type="time"
              step="1"
              class="input-field"
            />
          </div>
          
          <div class="input-group">
            <label class="input-label">时区</label>
            <select v-model="selectedTimezone" class="input-field">
              <option value="local">本地时区</option>
              <option value="UTC">UTC (协调世界时)</option>
              <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
              <option value="America/New_York">America/New_York (纽约时间)</option>
              <option value="Europe/London">Europe/London (伦敦时间)</option>
              <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
            </select>
          </div>
          
          <div class="action-buttons">
            <button
              @click="useCurrentDateTime"
              class="btn-helper"
            >
              使用当前时间
            </button>
            
            <button
              @click="convertDateToTimestamp"
              class="btn-primary"
              :disabled="!dateInput || !timeInput"
            >
              <span class="btn-icon">🔄</span>
              转换
            </button>
          </div>
          
          <ResultDisplay
            v-if="dateResult"
            :content="dateResult"
            :type="'text'"
            :allow-copy="true"
            title="转换结果"
            @clear="clearDateResult"
          />
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div class="quick-actions-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⚡</span>
          快捷操作
        </h2>
        
        <div class="quick-actions-grid">
          <button
            v-for="action in quickActions"
            :key="action.label"
            @click="useQuickAction(action.offset)"
            class="quick-action-btn"
          >
            <div class="action-label">{{ action.label }}</div>
            <div class="action-desc">{{ action.desc }}</div>
          </button>
        </div>
      </div>
      
      <!-- 常用格式 -->
      <div class="formats-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📋</span>
          常用时间格式
        </h2>
        
        <div class="formats-grid">
          <div
            v-for="format in commonFormats"
            :key="format.label"
            class="format-card"
          >
            <div class="format-label">{{ format.label }}</div>
            <div class="format-value">{{ format.value }}</div>
            <button
              @click="copyFormat(format.value)"
              class="format-copy-btn"
            >
              📋
            </button>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const currentTimestamp = ref(0)
const currentDateTime = ref('')
const timestampInput = ref('')
const timestampResult = ref('')
const dateInput = ref('')
const timeInput = ref('')
const selectedTimezone = ref('local')
const dateResult = ref('')

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 快捷操作配置
const quickActions = [
  { label: '昨天', desc: '-1d', offset: -86400 },
  { label: '今天', desc: '0d', offset: 0 },
  { label: '明天', desc: '+1d', offset: 86400 },
  { label: '下周', desc: '+7d', offset: 604800 },
  { label: '下月', desc: '+30d', offset: 2592000 },
  { label: '明年', desc: '+365d', offset: 31536000 }
]

// 计算属性 - 常用格式
const commonFormats = computed(() => {
  const now = new Date()
  return [
    {
      label: 'ISO 8601',
      value: now.toISOString()
    },
    {
      label: 'RFC 2822',
      value: now.toString()
    },
    {
      label: 'Unix 时间戳',
      value: Math.floor(now.getTime() / 1000).toString()
    },
    {
      label: '毫秒时间戳',
      value: now.getTime().toString()
    },
    {
      label: 'UTC 字符串',
      value: now.toUTCString()
    },
    {
      label: '本地字符串',
      value: now.toLocaleString('zh-CN')
    }
  ]
})

// 定时器
let timeInterval = null

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTimestamp.value = Math.floor(now.getTime() / 1000)
  currentDateTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 实时转换时间戳（防抖）
const handleTimestampInput = debounce(() => {
  if (timestampInput.value) {
    convertTimestampToDate(true)
  }
}, 500)

// 时间戳转日期
const convertTimestampToDate = (silent = false) => {
  if (!timestampInput.value) {
    if (!silent) timestampResult.value = ''
    return
  }
  
  try {
    const timestamp = parseInt(timestampInput.value)
    let date
    
    // 判断是秒还是毫秒时间戳
    if (timestamp.toString().length === 10) {
      date = new Date(timestamp * 1000)
    } else if (timestamp.toString().length === 13) {
      date = new Date(timestamp)
    } else {
      throw new Error('无效的时间戳格式')
    }
    
    if (isNaN(date.getTime())) {
      throw new Error('无效的时间戳')
    }
    
    const result = [
      `本地时间: ${date.toLocaleString('zh-CN')}`,
      `UTC时间: ${date.toUTCString()}`,
      `ISO格式: ${date.toISOString()}`,
      `年月日: ${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
      `时分秒: ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
    ].join('\n')
    
    timestampResult.value = result
    
    if (!silent) {
      addToHistory({
        toolId: 'timestamp-converter',
        toolName: '时间戳转换器',
        input: timestampInput.value,
        output: result,
        metadata: { action: 'timestamp-to-date' }
      })
    }
    
  } catch (error) {
    timestampResult.value = `转换失败: ${error.message}`
  }
}

// 日期转时间戳
const convertDateToTimestamp = () => {
  if (!dateInput.value || !timeInput.value) {
    dateResult.value = '请输入完整的日期和时间'
    return
  }
  
  try {
    const dateTimeString = `${dateInput.value}T${timeInput.value}`
    let date
    
    if (selectedTimezone.value === 'local') {
      date = new Date(dateTimeString)
    } else if (selectedTimezone.value === 'UTC') {
      date = new Date(dateTimeString + 'Z')
    } else {
      // 对于其他时区，这里简化处理
      date = new Date(dateTimeString)
    }
    
    if (isNaN(date.getTime())) {
      throw new Error('无效的日期时间')
    }
    
    const timestamp = Math.floor(date.getTime() / 1000)
    const milliseconds = date.getTime()
    
    const result = [
      `Unix时间戳: ${timestamp}`,
      `毫秒时间戳: ${milliseconds}`,
      `ISO格式: ${date.toISOString()}`,
      `UTC时间: ${date.toUTCString()}`,
      `本地时间: ${date.toLocaleString('zh-CN')}`
    ].join('\n')
    
    dateResult.value = result
    
    addToHistory({
      toolId: 'timestamp-converter',
      toolName: '时间戳转换器',
      input: `${dateInput.value} ${timeInput.value} (${selectedTimezone.value})`,
      output: result,
      metadata: { action: 'date-to-timestamp' }
    })
    
  } catch (error) {
    dateResult.value = `转换失败: ${error.message}`
  }
}

// 使用当前时间戳
const useCurrentTimestamp = () => {
  timestampInput.value = currentTimestamp.value.toString()
  convertTimestampToDate()
}

// 使用当前日期时间
const useCurrentDateTime = () => {
  const now = new Date()
  dateInput.value = now.toISOString().split('T')[0]
  timeInput.value = now.toTimeString().split(' ')[0]
  convertDateToTimestamp()
}

// 快捷操作
const useQuickAction = (offset) => {
  const targetTime = Math.floor(Date.now() / 1000) + offset
  timestampInput.value = targetTime.toString()
  convertTimestampToDate()
}

// 复制功能
const copyTimestamp = () => {
  copyToClipboard(currentTimestamp.value.toString())
}

const copyDateTime = () => {
  copyToClipboard(currentDateTime.value)
}

const copyFormat = (value) => {
  copyToClipboard(value)
}

// 清空结果
const clearTimestampResult = () => {
  timestampResult.value = ''
}

const clearDateResult = () => {
  dateResult.value = ''
}

// 组件挂载和卸载
onMounted(() => {
  updateCurrentTime()
  useCurrentDateTime()
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.current-time-section,
.converter-section,
.quick-actions-section,
.formats-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.time-card {
  @apply relative bg-white border-2 border-yellow-300 rounded-lg p-4 text-center;
}

.time-label {
  @apply text-sm text-gray-600 mb-2;
}

.time-value {
  @apply text-lg font-mono font-bold text-gray-800 mb-3;
}

.copy-btn,
.format-copy-btn {
  @apply bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600 transition-colors;
}

.input-group {
  @apply mb-4;
}

.input-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.input-field {
  @apply w-full px-3 py-2 border-2 border-yellow-300 rounded-lg focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200;
}

.input-with-button {
  @apply flex gap-2;
}

.input-with-button .input-field {
  @apply flex-1;
}

.btn-helper {
  @apply px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm;
}

.action-buttons {
  @apply flex gap-3 mb-4;
}

.btn-primary {
  @apply px-4 py-2 bg-yellow-500 text-white rounded-lg border-2 border-yellow-600 hover:bg-yellow-600 transition-all duration-200 font-semibold;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-icon {
  @apply mr-1;
}

.quick-actions-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3;
}

.quick-action-btn {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-3 text-center hover:bg-yellow-50 hover:border-yellow-400 transition-all duration-200;
}

.action-label {
  @apply text-sm font-medium text-gray-800;
}

.action-desc {
  @apply text-xs text-gray-500 mt-1;
}

.formats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.format-card {
  @apply relative bg-white border border-yellow-300 rounded-lg p-3;
}

.format-label {
  @apply text-sm font-medium text-gray-700 mb-2;
}

.format-value {
  @apply text-sm font-mono text-gray-800 break-all pr-8;
}

.format-copy-btn {
  @apply absolute top-2 right-2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-actions-grid {
    @apply grid-cols-2;
  }
  
  .formats-grid {
    @apply grid-cols-1;
  }
}
</style>
