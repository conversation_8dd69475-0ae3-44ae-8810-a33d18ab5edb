<template>
  <div class="file-upload">
    <!-- 上传区域 -->
    <div 
      class="upload-area"
      :class="{ 
        'drag-over': isDragOver,
        'has-files': files.length > 0,
        'disabled': disabled
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <input
        ref="fileInput"
        type="file"
        :multiple="multiple"
        :accept="accept"
        :disabled="disabled"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <div class="upload-content">
        <div class="upload-icon">
          <span v-if="!isLoading">📁</span>
          <span v-else class="loading-spinner">⏳</span>
        </div>
        
        <div class="upload-text">
          <p class="upload-title">
            {{ files.length > 0 ? `已选择 ${files.length} 个文件` : '点击或拖拽文件到此处' }}
          </p>
          <p class="upload-subtitle">
            {{ acceptText || '支持的文件类型: ' + (accept || '所有文件') }}
          </p>
          <p v-if="maxSize" class="upload-limit">
            最大文件大小: {{ formatFileSize(maxSize) }}
          </p>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="files.length > 0" class="file-list">
      <div 
        v-for="(file, index) in files" 
        :key="index"
        class="file-item"
        :class="{ 'file-error': file.error }"
      >
        <div class="file-info">
          <div class="file-icon">📄</div>
          <div class="file-details">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">
              {{ formatFileSize(file.size) }}
              <span v-if="file.type" class="file-type">• {{ file.type }}</span>
            </div>
            <div v-if="file.error" class="file-error-msg">
              ❌ {{ file.error }}
            </div>
          </div>
        </div>
        
        <div class="file-actions">
          <button 
            @click="removeFile(index)"
            class="btn-remove"
            :disabled="disabled"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="isLoading && progress > 0" class="progress-bar">
      <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      <span class="progress-text">{{ progress }}%</span>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-message">
      ❌ {{ error }}
    </div>

    <!-- 操作按钮 -->
    <div v-if="files.length > 0" class="upload-actions">
      <button 
        @click="processFiles"
        class="btn-process"
        :disabled="disabled || isLoading || hasErrors"
      >
        <span v-if="!isLoading">🚀 处理文件</span>
        <span v-else>⏳ 处理中...</span>
      </button>
      
      <button 
        @click="clearFiles"
        class="btn-clear"
        :disabled="disabled || isLoading"
      >
        🗑️ 清空
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useFileHandler } from '@/composables/useFileHandler'

const props = defineProps({
  multiple: {
    type: Boolean,
    default: false
  },
  accept: {
    type: String,
    default: ''
  },
  acceptText: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  disabled: {
    type: Boolean,
    default: false
  },
  autoProcess: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['files-selected', 'files-processed', 'error'])

const { 
  isLoading, 
  error, 
  progress, 
  readFile, 
  validateFileType, 
  validateFileSize, 
  formatFileSize,
  clearError 
} = useFileHandler()

const fileInput = ref(null)
const files = ref([])
const isDragOver = ref(false)

const hasErrors = computed(() => {
  return files.value.some(file => file.error)
})

const acceptedTypes = computed(() => {
  if (!props.accept) return []
  return props.accept.split(',').map(type => type.trim())
})

const triggerFileInput = () => {
  if (!props.disabled) {
    fileInput.value?.click()
  }
}

const handleFileSelect = (event) => {
  const selectedFiles = Array.from(event.target.files || [])
  addFiles(selectedFiles)
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  if (props.disabled) return
  
  const droppedFiles = Array.from(event.dataTransfer.files || [])
  addFiles(droppedFiles)
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDragEnter = (event) => {
  event.preventDefault()
  if (!props.disabled) {
    isDragOver.value = true
  }
}

const handleDragLeave = (event) => {
  event.preventDefault()
  // 只有当离开整个拖拽区域时才设置为false
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

const addFiles = (newFiles) => {
  clearError()
  
  const validFiles = newFiles.map(file => {
    const fileObj = {
      name: file.name,
      size: file.size,
      type: file.type,
      file: file,
      error: null
    }
    
    // 验证文件类型
    if (acceptedTypes.value.length > 0 && !validateFileType(file, acceptedTypes.value)) {
      fileObj.error = '不支持的文件类型'
    }
    
    // 验证文件大小
    if (!validateFileSize(file, props.maxSize)) {
      fileObj.error = `文件大小超过限制 (${formatFileSize(props.maxSize)})`
    }
    
    return fileObj
  })
  
  if (props.multiple) {
    files.value.push(...validFiles)
  } else {
    files.value = validFiles.slice(0, 1)
  }
  
  emit('files-selected', files.value)
  
  if (props.autoProcess && !hasErrors.value) {
    processFiles()
  }
}

const removeFile = (index) => {
  files.value.splice(index, 1)
  emit('files-selected', files.value)
}

const clearFiles = () => {
  files.value = []
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  clearError()
  emit('files-selected', [])
}

const processFiles = async () => {
  if (hasErrors.value) return
  
  try {
    const results = []
    
    for (const fileObj of files.value) {
      if (fileObj.error) continue
      
      const content = await readFile(fileObj.file, 'text')
      results.push({
        ...fileObj,
        content
      })
    }
    
    emit('files-processed', results)
  } catch (err) {
    emit('error', err.message)
  }
}
</script>

<style scoped>
.file-upload {
  @apply space-y-4;
}

.upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-all duration-200;
  background: linear-gradient(145deg, #f8fafc, #f1f5f9);
}

.upload-area:hover:not(.disabled) {
  @apply border-blue-400 bg-blue-50;
}

.upload-area.drag-over {
  @apply border-blue-500 bg-blue-100;
}

.upload-area.has-files {
  @apply border-green-400 bg-green-50;
}

.upload-area.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.upload-content {
  @apply space-y-3;
}

.upload-icon {
  @apply text-4xl;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.upload-title {
  @apply text-lg font-semibold text-gray-700;
}

.upload-subtitle {
  @apply text-sm text-gray-500;
}

.upload-limit {
  @apply text-xs text-gray-400;
}

.file-list {
  @apply space-y-2;
}

.file-item {
  @apply flex items-center justify-between p-3 bg-white rounded border;
}

.file-item.file-error {
  @apply border-red-300 bg-red-50;
}

.file-info {
  @apply flex items-center gap-3 flex-1;
}

.file-icon {
  @apply text-2xl;
}

.file-name {
  @apply font-medium text-gray-800;
}

.file-meta {
  @apply text-sm text-gray-500;
}

.file-type {
  @apply text-gray-400;
}

.file-error-msg {
  @apply text-sm text-red-600 mt-1;
}

.btn-remove {
  @apply p-1 text-red-500 hover:bg-red-100 rounded transition-colors;
}

.progress-bar {
  @apply relative bg-gray-200 rounded-full h-4 overflow-hidden;
}

.progress-fill {
  @apply bg-blue-500 h-full transition-all duration-300;
}

.progress-text {
  @apply absolute inset-0 flex items-center justify-center text-xs font-medium text-white;
}

.error-message {
  @apply p-3 bg-red-50 border border-red-200 rounded text-red-700;
}

.upload-actions {
  @apply flex gap-3;
}

.btn-process,
.btn-clear {
  @apply px-4 py-2 rounded border-2 font-medium transition-all duration-200;
}

.btn-process {
  @apply bg-blue-500 text-white border-blue-600 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-clear {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
