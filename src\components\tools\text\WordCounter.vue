<template>
  <ToolLayout
    title="字数统计器"
    description="文本字数、字符、段落统计分析工具"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📝</span>
          输入文本
        </h2>
        
        <textarea 
          v-model="inputText"
          class="text-input"
          placeholder="请输入要统计的文本..."
          @input="handleInput"
        ></textarea>
        
        <!-- 文件上传 -->
        <div class="file-upload-section">
          <FileUpload
            :accept="'.txt,.md,.doc,.docx'"
            :max-size="5 * 1024 * 1024"
            accept-text="支持文本文件 (最大5MB)"
            @files-processed="handleFileUpload"
          />
        </div>
        
        <!-- 目标计数器 -->
        <div class="target-section">
          <h3 class="target-title">
            <span class="title-icon">🎯</span>
            目标计数器
          </h3>
          
          <div class="target-inputs">
            <div class="target-input-group">
              <label class="target-label">目标字数:</label>
              <input
                v-model.number="targetWords"
                type="number"
                min="1"
                class="target-input"
              />
            </div>
            
            <div class="target-input-group">
              <label class="target-label">目标字符数:</label>
              <input
                v-model.number="targetChars"
                type="number"
                min="1"
                class="target-input"
              />
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-label">字数进度</span>
                <span class="progress-percentage">{{ wordProgress }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: Math.min(wordProgress, 100) + '%' }"
                  :class="{ 'progress-complete': wordProgress >= 100 }"
                ></div>
              </div>
              <div class="progress-status">{{ stats.words }} / {{ targetWords }} 字</div>
            </div>
            
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-label">字符进度</span>
                <span class="progress-percentage">{{ charProgress }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: Math.min(charProgress, 100) + '%' }"
                  :class="{ 'progress-complete': charProgress >= 100 }"
                ></div>
              </div>
              <div class="progress-status">{{ stats.characters }} / {{ targetChars }} 字符</div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button @click="clearAll" class="btn-secondary">
            <span class="btn-icon">🗑️</span>
            清空
          </button>
          
          <button @click="exportStats" class="btn-primary">
            <span class="btn-icon">📊</span>
            导出统计
          </button>
        </div>
      </div>
      
      <!-- 统计结果区域 -->
      <div class="stats-section">
        <!-- 基础统计 -->
        <div class="stats-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">📊</span>
            基础统计
          </h3>
          
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number">{{ stats.characters }}</div>
              <div class="stat-label">字符数</div>
              <div class="stat-description">包含空格</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-number">{{ stats.charactersNoSpaces }}</div>
              <div class="stat-label">字符数</div>
              <div class="stat-description">不含空格</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-number">{{ stats.words }}</div>
              <div class="stat-label">单词数</div>
              <div class="stat-description">空格分隔</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-number">{{ stats.sentences }}</div>
              <div class="stat-label">句子数</div>
              <div class="stat-description">标点分隔</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-number">{{ stats.paragraphs }}</div>
              <div class="stat-label">段落数</div>
              <div class="stat-description">换行分隔</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-number">{{ stats.lines }}</div>
              <div class="stat-label">行数</div>
              <div class="stat-description">总行数</div>
            </div>
          </div>
        </div>
        
        <!-- 阅读时间 -->
        <div class="reading-time-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">⏱️</span>
            阅读时间
          </h3>
          
          <div class="reading-time-list">
            <div class="reading-time-item">
              <span class="reading-label">慢速阅读 (150字/分)</span>
              <span class="reading-value">{{ readingTimes.slow }}</span>
            </div>
            
            <div class="reading-time-item">
              <span class="reading-label">正常阅读 (250字/分)</span>
              <span class="reading-value">{{ readingTimes.normal }}</span>
            </div>
            
            <div class="reading-time-item">
              <span class="reading-label">快速阅读 (400字/分)</span>
              <span class="reading-value">{{ readingTimes.fast }}</span>
            </div>
          </div>
        </div>
        
        <!-- 字符分析 -->
        <div class="char-analysis-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">🔍</span>
            字符分析
          </h3>
          
          <div class="char-analysis-grid">
            <div class="analysis-item">
              <span class="analysis-label">中文字符</span>
              <span class="analysis-value">{{ charAnalysis.chinese }}</span>
            </div>
            
            <div class="analysis-item">
              <span class="analysis-label">英文字符</span>
              <span class="analysis-value">{{ charAnalysis.english }}</span>
            </div>
            
            <div class="analysis-item">
              <span class="analysis-label">数字字符</span>
              <span class="analysis-value">{{ charAnalysis.numbers }}</span>
            </div>
            
            <div class="analysis-item">
              <span class="analysis-label">标点符号</span>
              <span class="analysis-value">{{ charAnalysis.punctuation }}</span>
            </div>
            
            <div class="analysis-item">
              <span class="analysis-label">空格字符</span>
              <span class="analysis-value">{{ charAnalysis.spaces }}</span>
            </div>
            
            <div class="analysis-item">
              <span class="analysis-label">其他字符</span>
              <span class="analysis-value">{{ charAnalysis.others }}</span>
            </div>
          </div>
        </div>
        
        <!-- 词频分析 -->
        <div v-if="wordFrequency.length > 0" class="word-frequency-group sketch-border">
          <h3 class="group-title">
            <span class="title-icon">📈</span>
            词频分析 (前10)
          </h3>
          
          <div class="word-frequency-list">
            <div
              v-for="(item, index) in wordFrequency.slice(0, 10)"
              :key="item.word"
              class="frequency-item"
            >
              <span class="frequency-rank">{{ index + 1 }}</span>
              <span class="frequency-word">{{ item.word }}</span>
              <span class="frequency-count">{{ item.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { useFileHandler } from '@/composables/useFileHandler'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const inputText = ref('')
const targetWords = ref(500)
const targetChars = ref(2000)

// 组合式函数
const { downloadJson } = useFileHandler()
const { addToHistory } = useToolHistory()

// 计算属性 - 基础统计
const stats = computed(() => {
  if (!inputText.value) {
    return {
      characters: 0,
      charactersNoSpaces: 0,
      words: 0,
      sentences: 0,
      paragraphs: 0,
      lines: 0
    }
  }
  
  const text = inputText.value
  
  return {
    characters: text.length,
    charactersNoSpaces: text.replace(/\s/g, '').length,
    words: text.trim() ? text.trim().split(/\s+/).length : 0,
    sentences: text.split(/[.!?]+/).filter(s => s.trim().length > 0).length,
    paragraphs: text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length,
    lines: text.split('\n').length
  }
})

// 计算属性 - 进度
const wordProgress = computed(() => {
  return targetWords.value > 0 ? Math.round((stats.value.words / targetWords.value) * 100) : 0
})

const charProgress = computed(() => {
  return targetChars.value > 0 ? Math.round((stats.value.characters / targetChars.value) * 100) : 0
})

// 计算属性 - 阅读时间
const readingTimes = computed(() => {
  const words = stats.value.words
  
  const formatTime = (minutes) => {
    if (minutes < 1) return '不到1分钟'
    if (minutes < 60) return `${Math.round(minutes)}分钟`
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return `${hours}小时${mins}分钟`
  }
  
  return {
    slow: formatTime(words / 150),
    normal: formatTime(words / 250),
    fast: formatTime(words / 400)
  }
})

// 计算属性 - 字符分析
const charAnalysis = computed(() => {
  if (!inputText.value) {
    return {
      chinese: 0,
      english: 0,
      numbers: 0,
      punctuation: 0,
      spaces: 0,
      others: 0
    }
  }
  
  const text = inputText.value
  let chinese = 0, english = 0, numbers = 0, punctuation = 0, spaces = 0, others = 0
  
  for (const char of text) {
    if (/[\u4e00-\u9fff]/.test(char)) {
      chinese++
    } else if (/[a-zA-Z]/.test(char)) {
      english++
    } else if (/[0-9]/.test(char)) {
      numbers++
    } else if (/[.,;:!?'"()[\]{}]/.test(char)) {
      punctuation++
    } else if (/\s/.test(char)) {
      spaces++
    } else {
      others++
    }
  }
  
  return { chinese, english, numbers, punctuation, spaces, others }
})

// 计算属性 - 词频分析
const wordFrequency = computed(() => {
  if (!inputText.value) return []
  
  const words = inputText.value
    .toLowerCase()
    .replace(/[^\w\s\u4e00-\u9fff]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 1)
  
  const frequency = {}
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1
  })
  
  return Object.entries(frequency)
    .map(([word, count]) => ({ word, count }))
    .sort((a, b) => b.count - a.count)
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时统计（防抖）
const handleInput = debounce(() => {
  if (inputText.value.trim()) {
    addToHistory({
      toolId: 'word-counter',
      toolName: '字数统计器',
      input: inputText.value.substring(0, 100) + (inputText.value.length > 100 ? '...' : ''),
      output: `字符: ${stats.value.characters}, 单词: ${stats.value.words}, 句子: ${stats.value.sentences}`,
      metadata: { 
        characters: stats.value.characters,
        words: stats.value.words,
        sentences: stats.value.sentences
      }
    })
  }
}, 2000)

// 处理文件上传
const handleFileUpload = (files) => {
  if (files.length > 0 && files[0].content) {
    inputText.value = files[0].content
  }
}

// 导出统计
const exportStats = () => {
  const exportData = {
    text: inputText.value,
    timestamp: new Date().toISOString(),
    statistics: {
      basic: stats.value,
      readingTimes: readingTimes.value,
      characterAnalysis: charAnalysis.value,
      wordFrequency: wordFrequency.value.slice(0, 20),
      targets: {
        targetWords: targetWords.value,
        targetChars: targetChars.value,
        wordProgress: wordProgress.value,
        charProgress: charProgress.value
      }
    }
  }
  
  downloadJson(exportData, 'text-statistics.json')
}

// 清空所有
const clearAll = () => {
  inputText.value = ''
}
</script>

<style scoped>
.input-section,
.stats-group,
.reading-time-group,
.char-analysis-group,
.word-frequency-group {
  @apply bg-white p-6;
}

.stats-section {
  @apply space-y-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title,
.group-title,
.target-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.text-input {
  @apply w-full h-48 p-3 border-2 border-yellow-300 rounded-lg text-sm resize-y;
  @apply focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200;
  background: white;
  transition: border-color 0.3s ease;
}

.file-upload-section {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.target-section {
  @apply mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.target-inputs {
  @apply grid grid-cols-2 gap-4 mb-4;
}

.target-input-group {
  @apply space-y-2;
}

.target-label {
  @apply block text-sm font-medium text-gray-700;
}

.target-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-yellow-500;
}

.progress-section {
  @apply space-y-4;
}

.progress-item {
  @apply space-y-2;
}

.progress-header {
  @apply flex justify-between items-center;
}

.progress-label {
  @apply text-sm font-medium text-gray-700;
}

.progress-percentage {
  @apply text-sm font-bold text-yellow-600;
}

.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-3;
}

.progress-fill {
  @apply bg-yellow-500 h-3 rounded-full transition-all duration-300;
}

.progress-fill.progress-complete {
  @apply bg-green-500;
}

.progress-status {
  @apply text-xs text-gray-600 text-center;
}

.action-buttons {
  @apply flex gap-3 mt-6;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
}

.btn-primary {
  @apply bg-yellow-500 text-white border-yellow-600 hover:bg-yellow-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

.stats-grid {
  @apply grid grid-cols-2 gap-3;
}

.stat-card {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-4 text-center;
}

.stat-number {
  @apply text-3xl font-bold text-yellow-600 font-mono;
}

.stat-label {
  @apply text-sm font-medium text-gray-700 mt-2;
}

.stat-description {
  @apply text-xs text-gray-500 mt-1;
}

.reading-time-list {
  @apply space-y-3;
}

.reading-time-item {
  @apply flex justify-between items-center p-3 bg-white border border-yellow-300 rounded;
}

.reading-label {
  @apply text-sm text-gray-700;
}

.reading-value {
  @apply text-sm font-semibold text-yellow-600;
}

.char-analysis-grid {
  @apply grid grid-cols-2 gap-3;
}

.analysis-item {
  @apply flex justify-between items-center p-2 bg-white border border-yellow-300 rounded;
}

.analysis-label {
  @apply text-sm text-gray-700;
}

.analysis-value {
  @apply text-sm font-semibold text-yellow-600;
}

.word-frequency-list {
  @apply space-y-2;
}

.frequency-item {
  @apply flex items-center gap-3 p-2 bg-white border border-yellow-300 rounded;
}

.frequency-rank {
  @apply w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold;
}

.frequency-word {
  @apply flex-1 text-sm text-gray-700;
}

.frequency-count {
  @apply text-sm font-semibold text-yellow-600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .target-inputs {
    @apply grid-cols-1;
  }
  
  .stats-grid {
    @apply grid-cols-1;
  }
  
  .char-analysis-grid {
    @apply grid-cols-1;
  }
}
</style>
