<template>
  <ToolLayout
    title="UUID生成器"
    description="生成各种版本的UUID（通用唯一标识符）"
  >
    <div class="space-y-6">
      <!-- 控制面板 -->
      <div class="control-panel sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⚙️</span>
          生成设置
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- UUID版本选择 -->
          <div>
            <label class="block text-sm font-medium mb-2">UUID版本</label>
            <div class="version-tabs">
              <button
                v-for="version in versions"
                :key="version.value"
                @click="currentVersion = version.value"
                class="version-tab"
                :class="{ active: currentVersion === version.value }"
              >
                {{ version.label }}
              </button>
            </div>
          </div>
          
          <!-- 生成数量 -->
          <div>
            <label class="block text-sm font-medium mb-2">生成数量</label>
            <input
              v-model.number="generateCount"
              type="number"
              min="1"
              max="100"
              class="input-field"
            />
          </div>
          
          <!-- 输出格式 -->
          <div>
            <label class="block text-sm font-medium mb-2">输出格式</label>
            <select v-model="outputFormat" class="input-field">
              <option value="default">标准格式</option>
              <option value="uppercase">大写</option>
              <option value="lowercase">小写</option>
              <option value="nohyphens">无连字符</option>
              <option value="braces">花括号包围</option>
            </select>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button @click="generateUUIDs" class="btn-primary">
            <span class="btn-icon">🎲</span>
            生成UUID
          </button>
          
          <button @click="copyAllUUIDs" class="btn-success" :disabled="!generatedUUIDs.length">
            <span class="btn-icon">📋</span>
            复制全部
          </button>
          
          <button @click="clearAll" class="btn-secondary" :disabled="!generatedUUIDs.length">
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
      </div>
      
      <!-- UUID列表 -->
      <div v-if="generatedUUIDs.length" class="uuid-list sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📋</span>
          生成的UUID ({{ generatedUUIDs.length }})
        </h2>
        
        <div class="uuid-cards">
          <div
            v-for="(uuid, index) in generatedUUIDs"
            :key="index"
            class="uuid-card"
          >
            <div class="uuid-content">
              <div class="uuid-text">{{ uuid }}</div>
              <button
                @click="copyUUID(uuid, index)"
                class="copy-btn"
                :class="{ copied: copiedIndex === index }"
              >
                {{ copiedIndex === index ? '✅' : '📋' }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- UUID验证 -->
      <div class="validation-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">✅</span>
          UUID验证
        </h2>
        
        <div class="validation-input">
          <input
            v-model="validateInput"
            type="text"
            placeholder="输入UUID进行验证..."
            class="input-field"
            @input="handleValidationInput"
          />
          <button @click="validateUUID" class="btn-warning">
            验证
          </button>
        </div>
        
        <div v-if="validationResult" class="validation-result">
          <div :class="validationResult.isValid ? 'success-message' : 'error-message'">
            {{ validationResult.message }}
          </div>
          <div v-if="validationResult.details" class="validation-details">
            <div v-for="(value, key) in validationResult.details" :key="key" class="detail-item">
              <span class="detail-label">{{ key }}:</span>
              <span class="detail-value">{{ value }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div v-if="stats.totalGenerated > 0" class="stats-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📊</span>
          统计信息
        </h2>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalGenerated }}</div>
            <div class="stat-label">总生成数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.currentSession }}</div>
            <div class="stat-label">本次会话</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.averageTime }}ms</div>
            <div class="stat-label">平均耗时</div>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'
import { useLocalStorage } from '@/composables/useLocalStorage'

// 响应式数据
const currentVersion = ref('4')
const generateCount = ref(5)
const outputFormat = ref('default')
const generatedUUIDs = ref([])
const validateInput = ref('')
const validationResult = ref(null)
const copiedIndex = ref(-1)

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()
const { value: stats } = useLocalStorage('uuid_stats', {
  totalGenerated: 0,
  currentSession: 0,
  averageTime: 0,
  generationTimes: []
})

// UUID版本配置
const versions = [
  { value: '1', label: 'v1' },
  { value: '4', label: 'v4' },
  { value: 'nil', label: 'Nil' }
]

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 生成UUID v4
const generateUUIDv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 生成UUID v1（简化版）
const generateUUIDv1 = () => {
  const timestamp = Date.now()
  const timeHex = timestamp.toString(16).padStart(12, '0')
  const clockSeq = Math.random() * 0x3fff | 0
  const node = Array.from({length: 6}, () => Math.random() * 256 | 0)
    .map(x => x.toString(16).padStart(2, '0')).join('')
  
  return [
    timeHex.slice(-8),
    timeHex.slice(-12, -8),
    '1' + timeHex.slice(-15, -12),
    (clockSeq >> 8 | 0x80).toString(16).padStart(2, '0') + 
    (clockSeq & 0xff).toString(16).padStart(2, '0'),
    node
  ].join('-')
}

// 生成Nil UUID
const generateNilUUID = () => {
  return '00000000-0000-0000-0000-000000000000'
}

// 格式化UUID
const formatUUID = (uuid, format) => {
  switch (format) {
    case 'uppercase':
      return uuid.toUpperCase()
    case 'lowercase':
      return uuid.toLowerCase()
    case 'nohyphens':
      return uuid.replace(/-/g, '')
    case 'braces':
      return `{${uuid}}`
    default:
      return uuid
  }
}

// 生成UUIDs
const generateUUIDs = () => {
  const startTime = performance.now()
  const count = Math.min(Math.max(generateCount.value, 1), 100)
  
  try {
    const uuids = []
    
    for (let i = 0; i < count; i++) {
      let uuid
      
      switch (currentVersion.value) {
        case '1':
          uuid = generateUUIDv1()
          break
        case '4':
          uuid = generateUUIDv4()
          break
        case 'nil':
          uuid = generateNilUUID()
          break
        default:
          uuid = generateUUIDv4()
      }
      
      uuids.push(formatUUID(uuid, outputFormat.value))
    }
    
    generatedUUIDs.value = uuids
    
    // 更新统计
    const generationTime = performance.now() - startTime
    stats.value.totalGenerated += count
    stats.value.currentSession += count
    stats.value.generationTimes.push(generationTime)
    
    // 计算平均时间（保留最近100次记录）
    if (stats.value.generationTimes.length > 100) {
      stats.value.generationTimes = stats.value.generationTimes.slice(-100)
    }
    stats.value.averageTime = Math.round(
      stats.value.generationTimes.reduce((a, b) => a + b, 0) / stats.value.generationTimes.length
    )
    
    // 添加到历史记录
    addToHistory({
      toolId: 'uuid-generator',
      toolName: 'UUID生成器',
      input: `版本: ${currentVersion.value}, 数量: ${count}, 格式: ${outputFormat.value}`,
      output: uuids.join('\n'),
      metadata: { 
        version: currentVersion.value,
        count: count,
        format: outputFormat.value
      }
    })
    
  } catch (error) {
    console.error('生成UUID失败:', error)
  }
}

// 复制单个UUID
const copyUUID = async (uuid, index) => {
  const result = await copyToClipboard(uuid)
  if (result.success) {
    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  }
}

// 复制所有UUID
const copyAllUUIDs = async () => {
  const allUUIDs = generatedUUIDs.value.join('\n')
  await copyToClipboard(allUUIDs)
}

// 清空所有
const clearAll = () => {
  generatedUUIDs.value = []
  validationResult.value = null
}

// 验证UUID输入处理
const handleValidationInput = debounce(() => {
  if (validateInput.value.trim()) {
    validateUUID()
  } else {
    validationResult.value = null
  }
}, 300)

// 验证UUID
const validateUUID = () => {
  const input = validateInput.value.trim()
  if (!input) {
    validationResult.value = null
    return
  }
  
  // UUID正则表达式
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  const nilUuidRegex = /^00000000-0000-0000-0000-000000000000$/
  
  if (nilUuidRegex.test(input)) {
    validationResult.value = {
      isValid: true,
      message: '✅ 有效的Nil UUID',
      details: {
        '类型': 'Nil UUID',
        '版本': 'N/A',
        '格式': '标准格式'
      }
    }
  } else if (uuidRegex.test(input)) {
    const version = input.charAt(14)
    validationResult.value = {
      isValid: true,
      message: `✅ 有效的UUID v${version}`,
      details: {
        '类型': `UUID v${version}`,
        '版本': version,
        '格式': '标准格式',
        '长度': `${input.length} 字符`
      }
    }
  } else {
    validationResult.value = {
      isValid: false,
      message: '❌ 无效的UUID格式',
      details: null
    }
  }
}

// 组件挂载时生成初始UUID
onMounted(() => {
  generateUUIDs()
})
</script>

<style scoped>
.control-panel,
.uuid-list,
.validation-section,
.stats-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.version-tabs {
  @apply flex gap-2;
}

.version-tab {
  @apply px-3 py-2 border-2 border-yellow-300 rounded-lg bg-white text-yellow-700 font-medium transition-all duration-200 cursor-pointer;
}

.version-tab.active {
  @apply bg-yellow-500 text-white border-yellow-600;
}

.version-tab:hover {
  @apply transform -translate-y-0.5 shadow-md;
}

.input-field {
  @apply w-full px-3 py-2 border-2 border-yellow-300 rounded-lg focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-success,
.btn-warning,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-yellow-500 text-white border-yellow-600 hover:bg-yellow-600;
}

.btn-success {
  @apply bg-green-500 text-white border-green-600 hover:bg-green-600;
}

.btn-warning {
  @apply bg-orange-500 text-white border-orange-600 hover:bg-orange-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

.uuid-cards {
  @apply space-y-3;
}

.uuid-card {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-4 relative transition-all duration-200;
}

.uuid-card:hover {
  @apply transform -translate-y-1 shadow-lg;
}

.uuid-content {
  @apply relative;
}

.uuid-text {
  @apply font-mono text-sm bg-gray-50 p-3 rounded border break-all;
}

.copy-btn {
  @apply absolute top-2 right-2 bg-yellow-500 text-white border-none rounded px-2 py-1 text-xs cursor-pointer transition-all duration-200;
}

.copy-btn:hover {
  @apply bg-yellow-600 transform scale-105;
}

.copy-btn.copied {
  @apply bg-green-500;
}

.validation-input {
  @apply flex gap-3;
}

.validation-result {
  @apply mt-4 space-y-3;
}

.success-message {
  @apply text-green-700 bg-green-50 border border-green-200 rounded p-3;
}

.error-message {
  @apply text-red-700 bg-red-50 border border-red-200 rounded p-3;
}

.validation-details {
  @apply bg-gray-50 border border-gray-200 rounded p-3 space-y-2;
}

.detail-item {
  @apply flex justify-between;
}

.detail-label {
  @apply font-medium text-gray-600;
}

.detail-value {
  @apply text-gray-800;
}

.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.stat-item {
  @apply bg-white p-4 rounded-lg border border-yellow-300 text-center;
}

.stat-value {
  @apply text-2xl font-bold text-yellow-700;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
  
  .validation-input {
    @apply flex-col;
  }
}
</style>
