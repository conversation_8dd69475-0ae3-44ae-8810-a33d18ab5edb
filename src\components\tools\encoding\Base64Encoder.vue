<template>
  <ToolLayout
    title="Base64编解码器"
    description="Base64编码和解码工具，支持文本和文件处理"
  >
    <div class="space-y-6">
      <!-- 模式切换 -->
      <div class="mode-tabs sketch-border">
        <div class="tab-buttons">
          <button
            @click="currentMode = 'encode'"
            class="tab-button"
            :class="{ active: currentMode === 'encode' }"
          >
            <span class="tab-icon">🔐</span>
            编码
          </button>
          <button
            @click="currentMode = 'decode'"
            class="tab-button"
            :class="{ active: currentMode === 'decode' }"
          >
            <span class="tab-icon">🔓</span>
            解码
          </button>
        </div>
      </div>

      <!-- 编码模式 -->
      <div v-if="currentMode === 'encode'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 输入区域 -->
        <div class="input-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📝</span>
            原始文本
          </h2>
          
          <textarea 
            v-model="encodeInput"
            class="text-input"
            placeholder="请输入要编码的文本..."
            @input="handleEncodeInput"
          ></textarea>
          
          <!-- 文件上传 -->
          <div class="file-upload-section">
            <FileUpload
              :accept="'text/*'"
              :max-size="1024 * 1024"
              accept-text="支持文本文件"
              @files-processed="handleFileUpload"
            />
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button 
              @click="encodeText"
              class="btn-primary"
              :disabled="!encodeInput.trim()"
            >
              <span class="btn-icon">🔐</span>
              编码
            </button>
            
            <button 
              @click="clearEncode"
              class="btn-secondary"
            >
              <span class="btn-icon">🗑️</span>
              清空
            </button>
          </div>
        </div>
        
        <!-- 输出区域 -->
        <div class="output-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📋</span>
            Base64编码结果
          </h2>
          
          <ResultDisplay
            :content="encodeOutput"
            :type="'text'"
            :loading="isProcessing"
            :error="error"
            :allow-copy="true"
            :allow-download="true"
            :download-filename="'base64_encoded.txt'"
            :stats="encodeStats"
            @clear="clearEncodeOutput"
          />
        </div>
      </div>

      <!-- 解码模式 -->
      <div v-if="currentMode === 'decode'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 输入区域 -->
        <div class="input-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">🔐</span>
            Base64编码文本
          </h2>
          
          <textarea 
            v-model="decodeInput"
            class="text-input"
            placeholder="请输入Base64编码的文本..."
            @input="handleDecodeInput"
          ></textarea>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button 
              @click="decodeText"
              class="btn-primary"
              :disabled="!decodeInput.trim()"
            >
              <span class="btn-icon">🔓</span>
              解码
            </button>
            
            <button 
              @click="clearDecode"
              class="btn-secondary"
            >
              <span class="btn-icon">🗑️</span>
              清空
            </button>
          </div>
        </div>
        
        <!-- 输出区域 -->
        <div class="output-section sketch-border">
          <h2 class="section-title">
            <span class="title-icon">📋</span>
            解码结果
          </h2>
          
          <ResultDisplay
            :content="decodeOutput"
            :type="'text'"
            :loading="isProcessing"
            :error="error"
            :allow-copy="true"
            :allow-download="true"
            :download-filename="'base64_decoded.txt'"
            :stats="decodeStats"
            @clear="clearDecodeOutput"
          />
        </div>
      </div>
      
      <!-- 功能说明 -->
      <div class="info-section sketch-border">
        <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <span class="mr-2">💡</span>
          Base64编码说明
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔐 编码功能</h4>
            <p>将任意文本转换为Base64编码格式，常用于数据传输和存储</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔓 解码功能</h4>
            <p>将Base64编码的文本还原为原始内容</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🌍 多语言支持</h4>
            <p>支持中文、英文、特殊字符等各种文本编码</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800 mb-2">🔒 安全可靠</h4>
            <p>本地处理，不上传数据，保护您的隐私安全</p>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const currentMode = ref('encode')
const encodeInput = ref('')
const encodeOutput = ref('')
const decodeInput = ref('')
const decodeOutput = ref('')
const error = ref('')
const isProcessing = ref(false)

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 计算属性
const encodeStats = computed(() => {
  if (!encodeOutput.value) return null
  
  const inputSize = new Blob([encodeInput.value]).size
  const outputSize = encodeOutput.value.length
  const expansion = inputSize > 0 ? ((outputSize - inputSize) / inputSize * 100).toFixed(1) : 0
  
  return {
    '原始大小': `${inputSize} 字节`,
    '编码大小': `${outputSize} 字符`,
    '大小增长': `${expansion}%`
  }
})

const decodeStats = computed(() => {
  if (!decodeOutput.value) return null
  
  const inputSize = decodeInput.value.length
  const outputSize = new Blob([decodeOutput.value]).size
  const compression = inputSize > 0 ? ((inputSize - outputSize) / inputSize * 100).toFixed(1) : 0
  
  return {
    '编码大小': `${inputSize} 字符`,
    '解码大小': `${outputSize} 字节`,
    '大小减少': `${compression}%`
  }
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时编码（防抖）
const handleEncodeInput = debounce(() => {
  if (encodeInput.value.trim()) {
    encodeText()
  }
}, 500)

// 实时解码（防抖）
const handleDecodeInput = debounce(() => {
  if (decodeInput.value.trim()) {
    decodeText()
  }
}, 500)

// Base64编码
const encodeText = () => {
  if (!encodeInput.value.trim()) {
    error.value = '请输入要编码的文本'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    // 使用 btoa 进行 Base64 编码，但需要先处理 Unicode 字符
    const encoded = btoa(unescape(encodeURIComponent(encodeInput.value)))
    encodeOutput.value = encoded
    
    // 添加到历史记录
    addToHistory({
      toolId: 'base64-encoder',
      toolName: 'Base64编解码器',
      input: encodeInput.value,
      output: encoded,
      metadata: { action: 'encode' }
    })
    
  } catch (err) {
    error.value = `编码失败: ${err.message}`
    encodeOutput.value = ''
  } finally {
    isProcessing.value = false
  }
}

// Base64解码
const decodeText = () => {
  if (!decodeInput.value.trim()) {
    error.value = '请输入Base64编码的文本'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    // 验证Base64格式
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/
    const cleanInput = decodeInput.value.replace(/\s/g, '') // 移除空白字符
    
    if (!base64Regex.test(cleanInput)) {
      throw new Error('无效的Base64格式')
    }
    
    // 使用 atob 进行 Base64 解码，然后处理 Unicode 字符
    const decoded = decodeURIComponent(escape(atob(cleanInput)))
    decodeOutput.value = decoded
    
    // 添加到历史记录
    addToHistory({
      toolId: 'base64-encoder',
      toolName: 'Base64编解码器',
      input: decodeInput.value,
      output: decoded,
      metadata: { action: 'decode' }
    })
    
  } catch (err) {
    error.value = `解码失败: ${err.message}`
    decodeOutput.value = ''
  } finally {
    isProcessing.value = false
  }
}

// 处理文件上传
const handleFileUpload = (files) => {
  if (files.length > 0 && files[0].content) {
    encodeInput.value = files[0].content
    encodeText()
  }
}

// 清空编码
const clearEncode = () => {
  encodeInput.value = ''
  encodeOutput.value = ''
  error.value = ''
}

// 清空解码
const clearDecode = () => {
  decodeInput.value = ''
  decodeOutput.value = ''
  error.value = ''
}

// 清空编码输出
const clearEncodeOutput = () => {
  encodeOutput.value = ''
  error.value = ''
}

// 清空解码输出
const clearDecodeOutput = () => {
  decodeOutput.value = ''
  error.value = ''
}
</script>

<style scoped>
.mode-tabs {
  @apply bg-white p-4;
}

.tab-buttons {
  @apply flex gap-2;
}

.tab-button {
  @apply px-4 py-2 border-2 border-orange-300 rounded-lg bg-white text-orange-700 font-medium transition-all duration-200 cursor-pointer;
}

.tab-button.active {
  @apply bg-orange-500 text-white border-orange-600;
}

.tab-button:hover {
  @apply transform -translate-y-0.5 shadow-md;
}

.tab-icon {
  @apply mr-2;
}

.input-section,
.output-section,
.info-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #d97706;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #f59e0b;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.text-input {
  @apply w-full h-64 p-3 border-2 border-orange-300 rounded-lg font-mono text-sm resize-none;
  @apply focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200;
  background: white;
  transition: border-color 0.3s ease;
}

.file-upload-section {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-orange-500 text-white border-orange-600 hover:bg-orange-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>
