import { ref } from 'vue'

export function useClipboard() {
  const isSupported = ref(!!navigator.clipboard)
  const isLoading = ref(false)
  const error = ref('')

  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   * @returns {Promise<{success: boolean, message: string}>}
   */
  const copyToClipboard = async (text) => {
    if (!text) {
      return { success: false, message: '没有内容可复制' }
    }

    isLoading.value = true
    error.value = ''

    try {
      if (navigator.clipboard && window.isSecureContext) {
        // 使用现代 Clipboard API
        await navigator.clipboard.writeText(text)
      } else {
        // 降级方案：使用传统方法
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        
        if (!successful) {
          throw new Error('复制命令执行失败')
        }
      }
      
      isLoading.value = false
      return { success: true, message: '复制成功' }
    } catch (err) {
      error.value = err.message || '复制失败'
      isLoading.value = false
      return { success: false, message: error.value }
    }
  }

  /**
   * 从剪贴板读取文本
   * @returns {Promise<string>}
   */
  const readFromClipboard = async () => {
    if (!navigator.clipboard) {
      console.warn('剪贴板 API 不支持')
      return ''
    }

    isLoading.value = true
    error.value = ''

    try {
      const text = await navigator.clipboard.readText()
      isLoading.value = false
      return text
    } catch (err) {
      error.value = err.message || '读取剪贴板失败'
      isLoading.value = false
      console.warn('无法读取剪贴板内容:', err)
      return ''
    }
  }

  /**
   * 检查剪贴板权限
   * @returns {Promise<boolean>}
   */
  const checkPermission = async () => {
    if (!navigator.permissions) {
      return false
    }

    try {
      const result = await navigator.permissions.query({ name: 'clipboard-read' })
      return result.state === 'granted'
    } catch (err) {
      console.warn('无法检查剪贴板权限:', err)
      return false
    }
  }

  /**
   * 复制对象为 JSON 字符串
   * @param {any} obj - 要复制的对象
   * @param {number} indent - JSON 缩进空格数
   * @returns {Promise<{success: boolean, message: string}>}
   */
  const copyObjectAsJson = async (obj, indent = 2) => {
    try {
      const jsonString = JSON.stringify(obj, null, indent)
      return await copyToClipboard(jsonString)
    } catch (err) {
      return { success: false, message: '对象序列化失败: ' + err.message }
    }
  }

  /**
   * 复制 HTML 内容
   * @param {string} html - HTML 字符串
   * @param {string} plainText - 纯文本备选
   * @returns {Promise<{success: boolean, message: string}>}
   */
  const copyHtml = async (html, plainText = '') => {
    if (!navigator.clipboard) {
      return await copyToClipboard(plainText || html.replace(/<[^>]*>/g, ''))
    }

    isLoading.value = true
    error.value = ''

    try {
      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([html], { type: 'text/html' }),
        'text/plain': new Blob([plainText || html.replace(/<[^>]*>/g, '')], { type: 'text/plain' })
      })
      
      await navigator.clipboard.write([clipboardItem])
      isLoading.value = false
      return { success: true, message: '复制成功' }
    } catch (err) {
      error.value = err.message || '复制 HTML 失败'
      isLoading.value = false
      // 降级到纯文本复制
      return await copyToClipboard(plainText || html.replace(/<[^>]*>/g, ''))
    }
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }

  return {
    isSupported,
    isLoading,
    error,
    copyToClipboard,
    readFromClipboard,
    checkPermission,
    copyObjectAsJson,
    copyHtml,
    clearError
  }
}
