<template>
  <ToolLayout
    title="二维码生成器"
    description="在线生成各种类型的二维码，支持文本、网址、WiFi等"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入配置区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⚙️</span>
          二维码配置
        </h2>
        
        <!-- 二维码类型 -->
        <div class="config-group">
          <label class="config-label">二维码类型</label>
          <div class="type-tabs">
            <button
              v-for="type in qrTypes"
              :key="type.value"
              @click="currentType = type.value"
              class="type-tab"
              :class="{ active: currentType === type.value }"
            >
              <span class="tab-icon">{{ type.icon }}</span>
              {{ type.label }}
            </button>
          </div>
        </div>
        
        <!-- 内容输入 -->
        <div class="config-group">
          <label class="config-label">{{ getContentLabel() }}</label>
          
          <!-- 文本类型 -->
          <textarea
            v-if="currentType === 'text'"
            v-model="content.text"
            class="content-input"
            placeholder="请输入要生成二维码的文本..."
            @input="handleContentChange"
          ></textarea>
          
          <!-- URL类型 -->
          <input
            v-else-if="currentType === 'url'"
            v-model="content.url"
            type="url"
            class="content-input"
            placeholder="https://example.com"
            @input="handleContentChange"
          />
          
          <!-- WiFi类型 -->
          <div v-else-if="currentType === 'wifi'" class="wifi-config">
            <input
              v-model="content.wifi.ssid"
              type="text"
              class="content-input"
              placeholder="WiFi名称 (SSID)"
              @input="handleContentChange"
            />
            <input
              v-model="content.wifi.password"
              type="text"
              class="content-input"
              placeholder="WiFi密码"
              @input="handleContentChange"
            />
            <select
              v-model="content.wifi.security"
              class="content-input"
              @change="handleContentChange"
            >
              <option value="WPA">WPA/WPA2</option>
              <option value="WEP">WEP</option>
              <option value="nopass">无密码</option>
            </select>
          </div>
          
          <!-- 联系人类型 -->
          <div v-else-if="currentType === 'contact'" class="contact-config">
            <input
              v-model="content.contact.name"
              type="text"
              class="content-input"
              placeholder="姓名"
              @input="handleContentChange"
            />
            <input
              v-model="content.contact.phone"
              type="tel"
              class="content-input"
              placeholder="电话号码"
              @input="handleContentChange"
            />
            <input
              v-model="content.contact.email"
              type="email"
              class="content-input"
              placeholder="邮箱地址"
              @input="handleContentChange"
            />
            <input
              v-model="content.contact.organization"
              type="text"
              class="content-input"
              placeholder="公司/组织"
              @input="handleContentChange"
            />
          </div>
          
          <!-- 短信类型 -->
          <div v-else-if="currentType === 'sms'" class="sms-config">
            <input
              v-model="content.sms.phone"
              type="tel"
              class="content-input"
              placeholder="手机号码"
              @input="handleContentChange"
            />
            <textarea
              v-model="content.sms.message"
              class="content-input"
              placeholder="短信内容"
              @input="handleContentChange"
            ></textarea>
          </div>
          
          <!-- 邮件类型 -->
          <div v-else-if="currentType === 'email'" class="email-config">
            <input
              v-model="content.email.to"
              type="email"
              class="content-input"
              placeholder="收件人邮箱"
              @input="handleContentChange"
            />
            <input
              v-model="content.email.subject"
              type="text"
              class="content-input"
              placeholder="邮件主题"
              @input="handleContentChange"
            />
            <textarea
              v-model="content.email.body"
              class="content-input"
              placeholder="邮件内容"
              @input="handleContentChange"
            ></textarea>
          </div>
        </div>
        
        <!-- 样式配置 -->
        <div class="config-group">
          <label class="config-label">样式配置</label>
          
          <div class="style-config">
            <div class="style-item">
              <label class="style-label">尺寸</label>
              <select v-model="qrConfig.size" @change="generateQR" class="style-input">
                <option value="200">小 (200x200)</option>
                <option value="300">中 (300x300)</option>
                <option value="400">大 (400x400)</option>
                <option value="500">特大 (500x500)</option>
              </select>
            </div>
            
            <div class="style-item">
              <label class="style-label">容错级别</label>
              <select v-model="qrConfig.errorCorrectionLevel" @change="generateQR" class="style-input">
                <option value="L">低 (7%)</option>
                <option value="M">中 (15%)</option>
                <option value="Q">高 (25%)</option>
                <option value="H">最高 (30%)</option>
              </select>
            </div>
            
            <div class="style-item">
              <label class="style-label">前景色</label>
              <input
                v-model="qrConfig.color.dark"
                type="color"
                class="color-input"
                @change="generateQR"
              />
            </div>
            
            <div class="style-item">
              <label class="style-label">背景色</label>
              <input
                v-model="qrConfig.color.light"
                type="color"
                class="color-input"
                @change="generateQR"
              />
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button @click="generateQR" class="btn-primary" :disabled="!getQRContent()">
            <span class="btn-icon">🎨</span>
            生成二维码
          </button>
          
          <button @click="clearAll" class="btn-secondary">
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
      </div>
      
      <!-- 预览和下载区域 -->
      <div class="preview-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">👁️</span>
          二维码预览
        </h2>
        
        <div class="qr-preview">
          <div v-if="isGenerating" class="loading-state">
            <div class="loading-spinner">⏳</div>
            <p>生成中...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">❌</div>
            <p>{{ error }}</p>
          </div>
          
          <div v-else-if="qrDataURL" class="qr-result">
            <img :src="qrDataURL" :alt="'二维码: ' + getQRContent()" class="qr-image" />
            
            <div class="qr-actions">
              <button @click="downloadQR" class="download-btn">
                <span class="btn-icon">💾</span>
                下载PNG
              </button>
              
              <button @click="copyQRImage" class="copy-btn">
                <span class="btn-icon">📋</span>
                复制图片
              </button>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <div class="empty-icon">📱</div>
            <p>请输入内容生成二维码</p>
          </div>
        </div>
        
        <!-- 二维码信息 -->
        <div v-if="qrDataURL" class="qr-info">
          <div class="info-item">
            <span class="info-label">内容:</span>
            <span class="info-value">{{ getQRContent().substring(0, 50) }}{{ getQRContent().length > 50 ? '...' : '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">尺寸:</span>
            <span class="info-value">{{ qrConfig.size }}x{{ qrConfig.size }}px</span>
          </div>
          <div class="info-item">
            <span class="info-label">容错级别:</span>
            <span class="info-value">{{ qrConfig.errorCorrectionLevel }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">类型:</span>
            <span class="info-value">{{ qrTypes.find(t => t.value === currentType)?.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import QRCode from 'qrcode'
import ToolLayout from '@/components/common/ToolLayout.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const currentType = ref('text')
const qrDataURL = ref('')
const isGenerating = ref(false)
const error = ref('')

// 二维码类型配置
const qrTypes = [
  { value: 'text', label: '文本', icon: '📝' },
  { value: 'url', label: '网址', icon: '🌐' },
  { value: 'wifi', label: 'WiFi', icon: '📶' },
  { value: 'contact', label: '联系人', icon: '👤' },
  { value: 'sms', label: '短信', icon: '💬' },
  { value: 'email', label: '邮件', icon: '📧' }
]

// 内容数据
const content = ref({
  text: '',
  url: '',
  wifi: {
    ssid: '',
    password: '',
    security: 'WPA'
  },
  contact: {
    name: '',
    phone: '',
    email: '',
    organization: ''
  },
  sms: {
    phone: '',
    message: ''
  },
  email: {
    to: '',
    subject: '',
    body: ''
  }
})

// 二维码配置
const qrConfig = ref({
  size: 300,
  errorCorrectionLevel: 'M',
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  }
})

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 获取内容标签
const getContentLabel = () => {
  const labels = {
    text: '文本内容',
    url: '网址链接',
    wifi: 'WiFi信息',
    contact: '联系人信息',
    sms: '短信信息',
    email: '邮件信息'
  }
  return labels[currentType.value] || '内容'
}

// 获取二维码内容
const getQRContent = () => {
  switch (currentType.value) {
    case 'text':
      return content.value.text
    case 'url':
      return content.value.url
    case 'wifi':
      const wifi = content.value.wifi
      return `WIFI:T:${wifi.security};S:${wifi.ssid};P:${wifi.password};;`
    case 'contact':
      const contact = content.value.contact
      return `BEGIN:VCARD\nVERSION:3.0\nFN:${contact.name}\nTEL:${contact.phone}\nEMAIL:${contact.email}\nORG:${contact.organization}\nEND:VCARD`
    case 'sms':
      const sms = content.value.sms
      return `sms:${sms.phone}?body=${encodeURIComponent(sms.message)}`
    case 'email':
      const email = content.value.email
      return `mailto:${email.to}?subject=${encodeURIComponent(email.subject)}&body=${encodeURIComponent(email.body)}`
    default:
      return ''
  }
}

// 实时生成（防抖）
const handleContentChange = debounce(() => {
  const qrContent = getQRContent()
  if (qrContent.trim()) {
    generateQR()
  }
}, 500)

// 生成二维码
const generateQR = async () => {
  const qrContent = getQRContent()
  if (!qrContent.trim()) {
    qrDataURL.value = ''
    return
  }
  
  isGenerating.value = true
  error.value = ''
  
  try {
    const options = {
      width: qrConfig.value.size,
      errorCorrectionLevel: qrConfig.value.errorCorrectionLevel,
      color: {
        dark: qrConfig.value.color.dark,
        light: qrConfig.value.color.light
      }
    }
    
    const dataURL = await QRCode.toDataURL(qrContent, options)
    qrDataURL.value = dataURL
    
    // 添加到历史记录
    addToHistory({
      toolId: 'qr-generator',
      toolName: '二维码生成器',
      input: qrContent.substring(0, 100) + (qrContent.length > 100 ? '...' : ''),
      output: '二维码图片已生成',
      metadata: { 
        type: currentType.value,
        size: qrConfig.value.size,
        errorLevel: qrConfig.value.errorCorrectionLevel
      }
    })
    
  } catch (err) {
    error.value = `生成失败: ${err.message}`
    qrDataURL.value = ''
  } finally {
    isGenerating.value = false
  }
}

// 下载二维码
const downloadQR = () => {
  if (!qrDataURL.value) return
  
  const link = document.createElement('a')
  link.download = `qrcode_${Date.now()}.png`
  link.href = qrDataURL.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 复制二维码图片
const copyQRImage = async () => {
  if (!qrDataURL.value) return
  
  try {
    const response = await fetch(qrDataURL.value)
    const blob = await response.blob()
    
    if (navigator.clipboard && window.ClipboardItem) {
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ])
    } else {
      // 降级方案：复制图片链接
      await copyToClipboard(qrDataURL.value)
    }
  } catch (err) {
    console.error('复制图片失败:', err)
    // 降级方案：复制图片链接
    await copyToClipboard(qrDataURL.value)
  }
}

// 清空所有
const clearAll = () => {
  content.value = {
    text: '',
    url: '',
    wifi: { ssid: '', password: '', security: 'WPA' },
    contact: { name: '', phone: '', email: '', organization: '' },
    sms: { phone: '', message: '' },
    email: { to: '', subject: '', body: '' }
  }
  qrDataURL.value = ''
  error.value = ''
}

// 组件挂载时加载示例
onMounted(() => {
  content.value.text = 'Hello, QR Code!'
  generateQR()
})
</script>

<style scoped>
.input-section,
.preview-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.config-group {
  @apply mb-6;
}

.config-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.type-tabs {
  @apply flex flex-wrap gap-2;
}

.type-tab {
  @apply px-3 py-2 border-2 border-yellow-300 rounded-lg bg-white text-yellow-700 font-medium transition-all duration-200 cursor-pointer;
}

.type-tab.active {
  @apply bg-yellow-500 text-white border-yellow-600;
}

.type-tab:hover {
  @apply transform -translate-y-0.5 shadow-md;
}

.tab-icon {
  @apply mr-1;
}

.content-input {
  @apply w-full px-3 py-2 border-2 border-yellow-300 rounded-lg focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 mb-3;
}

.wifi-config,
.contact-config,
.sms-config,
.email-config {
  @apply space-y-3;
}

.style-config {
  @apply grid grid-cols-2 gap-4;
}

.style-item {
  @apply space-y-2;
}

.style-label {
  @apply block text-xs font-medium text-gray-600;
}

.style-input {
  @apply w-full px-2 py-1 border border-yellow-300 rounded text-sm focus:outline-none focus:border-yellow-500;
}

.color-input {
  @apply w-full h-8 border border-yellow-300 rounded cursor-pointer;
}

.action-buttons {
  @apply flex gap-3;
}

.btn-primary,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-yellow-500 text-white border-yellow-600 hover:bg-yellow-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

.qr-preview {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-6 min-h-80 flex flex-col items-center justify-center;
}

.loading-state,
.error-state,
.empty-state {
  @apply flex flex-col items-center justify-center text-gray-500;
}

.loading-spinner {
  @apply text-2xl mb-2;
  animation: spin 1s linear infinite;
}

.error-state {
  @apply text-red-500;
}

.error-icon {
  @apply text-2xl mb-2;
}

.empty-icon {
  @apply text-4xl mb-2;
}

.qr-result {
  @apply text-center;
}

.qr-image {
  @apply max-w-full h-auto rounded border;
}

.qr-actions {
  @apply flex gap-3 mt-4;
}

.download-btn,
.copy-btn {
  @apply px-3 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors text-sm;
}

.qr-info {
  @apply mt-6 space-y-2 text-sm;
}

.info-item {
  @apply flex justify-between;
}

.info-label {
  @apply text-gray-600;
}

.info-value {
  @apply font-mono text-gray-800 break-all;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .type-tabs {
    @apply grid grid-cols-2 gap-2;
  }
  
  .style-config {
    @apply grid-cols-1;
  }
  
  .action-buttons {
    @apply flex-col;
  }
}
</style>
