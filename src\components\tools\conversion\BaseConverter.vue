<template>
  <ToolLayout
    title="进制转换器"
    description="数字进制转换工具，支持2-36进制互转"
  >
    <div class="space-y-6">
      <!-- 输入区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">🔢</span>
          输入数值
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="input-group">
            <label class="input-label">数值</label>
            <input
              v-model="inputNumber"
              type="text"
              class="input-field"
              placeholder="请输入数值..."
              @input="handleInput"
            />
          </div>
          
          <div class="input-group">
            <label class="input-label">源进制</label>
            <select v-model="sourceBase" @change="handleInput" class="input-field">
              <option value="2">二进制 (2)</option>
              <option value="8">八进制 (8)</option>
              <option value="10">十进制 (10)</option>
              <option value="16">十六进制 (16)</option>
              <option value="custom">自定义</option>
            </select>
          </div>
        </div>
        
        <!-- 自定义源进制 -->
        <div v-if="sourceBase === 'custom'" class="custom-base-input">
          <label class="input-label">自定义源进制 (2-36)</label>
          <input
            v-model.number="customSourceBase"
            type="number"
            min="2"
            max="36"
            class="input-field"
            @input="handleInput"
          />
        </div>
        
        <div class="action-buttons">
          <button @click="convertNumber" class="btn-primary">
            <span class="btn-icon">🔄</span>
            转换
          </button>
          
          <button @click="clearAll" class="btn-secondary">
            <span class="btn-icon">🗑️</span>
            清空
          </button>
          
          <button @click="swapBases" class="btn-helper" :disabled="sourceBase === 'custom'">
            <span class="btn-icon">🔄</span>
            交换进制
          </button>
        </div>
      </div>

      <!-- 常用进制结果 -->
      <div v-if="results.decimal !== null" class="results-section">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="result-card sketch-border">
            <div class="result-header">
              <span class="result-label">二进制 (2)</span>
              <button @click="copyResult(results.binary)" class="copy-btn">📋</button>
            </div>
            <div class="result-value">{{ results.binary }}</div>
          </div>
          
          <div class="result-card sketch-border">
            <div class="result-header">
              <span class="result-label">八进制 (8)</span>
              <button @click="copyResult(results.octal)" class="copy-btn">📋</button>
            </div>
            <div class="result-value">{{ results.octal }}</div>
          </div>
          
          <div class="result-card sketch-border">
            <div class="result-header">
              <span class="result-label">十进制 (10)</span>
              <button @click="copyResult(results.decimal)" class="copy-btn">📋</button>
            </div>
            <div class="result-value">{{ results.decimal }}</div>
          </div>
          
          <div class="result-card sketch-border">
            <div class="result-header">
              <span class="result-label">十六进制 (16)</span>
              <button @click="copyResult(results.hexadecimal)" class="copy-btn">📋</button>
            </div>
            <div class="result-value">{{ results.hexadecimal }}</div>
          </div>
        </div>
      </div>

      <!-- 自定义进制转换 -->
      <div class="custom-conversion-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⚙️</span>
          自定义进制转换
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="input-group">
            <label class="input-label">目标进制 (2-36)</label>
            <input
              v-model.number="targetBase"
              type="number"
              min="2"
              max="36"
              class="input-field"
            />
          </div>
          
          <div class="flex items-end">
            <button
              @click="convertToCustomBase"
              class="btn-primary w-full"
              :disabled="!inputNumber || !targetBase"
            >
              转换到 {{ targetBase }} 进制
            </button>
          </div>
        </div>
        
        <div v-if="customResult" class="custom-result">
          <div class="result-card sketch-border">
            <div class="result-header">
              <span class="result-label">{{ targetBase }} 进制结果</span>
              <button @click="copyResult(customResult)" class="copy-btn">📋</button>
            </div>
            <div class="result-value">{{ customResult }}</div>
          </div>
        </div>
      </div>

      <!-- 计算步骤 -->
      <div v-if="calculationSteps.length > 0" class="calculation-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">🧮</span>
          计算步骤
        </h2>
        
        <div class="steps-container">
          <div
            v-for="(step, index) in calculationSteps"
            :key="index"
            class="calculation-step"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">{{ step }}</div>
          </div>
        </div>
      </div>

      <!-- 快速转换 -->
      <div class="quick-convert-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">⚡</span>
          快速转换
        </h2>
        
        <div class="quick-convert-grid">
          <button
            v-for="example in quickExamples"
            :key="example.value + example.base"
            @click="useQuickExample(example)"
            class="quick-convert-btn"
          >
            <div class="example-value">{{ example.value }}</div>
            <div class="example-base">{{ example.label }}</div>
          </button>
        </div>
      </div>

      <!-- 二进制位显示 -->
      <div v-if="results.decimal !== null && results.decimal <= 255" class="binary-bits-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">🔢</span>
          二进制位显示 (8位)
        </h2>
        
        <div class="binary-bits">
          <div
            v-for="(bit, index) in binaryBits"
            :key="index"
            class="binary-bit"
            :class="{ active: bit === '1' }"
          >
            <div class="bit-value">{{ bit }}</div>
            <div class="bit-position">{{ 7 - index }}</div>
          </div>
        </div>
        
        <div class="bit-calculation">
          <div class="calculation-formula">
            {{ binaryCalculation }}
          </div>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const inputNumber = ref('')
const sourceBase = ref('10')
const customSourceBase = ref(10)
const targetBase = ref(16)
const customResult = ref('')
const calculationSteps = ref([])

// 结果对象
const results = ref({
  binary: null,
  octal: null,
  decimal: null,
  hexadecimal: null
})

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 快速示例
const quickExamples = [
  { value: '255', base: 10, label: '十进制' },
  { value: '11111111', base: 2, label: '二进制' },
  { value: 'FF', base: 16, label: '十六进制' },
  { value: '1024', base: 10, label: '十进制' },
  { value: '777', base: 8, label: '八进制' },
  { value: 'ABCD', base: 16, label: '十六进制' }
]

// 计算属性
const binaryBits = computed(() => {
  if (results.value.decimal === null || results.value.decimal > 255) return []
  
  const binary = results.value.decimal.toString(2).padStart(8, '0')
  return binary.split('')
})

const binaryCalculation = computed(() => {
  if (!binaryBits.value.length) return ''
  
  const terms = binaryBits.value.map((bit, index) => {
    const power = 7 - index
    return bit === '1' ? `2^${power}` : null
  }).filter(Boolean)
  
  return terms.join(' + ') + ` = ${results.value.decimal}`
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时转换（防抖）
const handleInput = debounce(() => {
  if (inputNumber.value.trim()) {
    convertNumber(true)
  }
}, 500)

// 验证输入是否符合指定进制
const validateInput = (value, base) => {
  const validChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'.slice(0, base)
  return value.toUpperCase().split('').every(char => validChars.includes(char))
}

// 转换数字
const convertNumber = (silent = false) => {
  if (!inputNumber.value.trim()) {
    if (!silent) clearResults()
    return
  }
  
  try {
    const currentBase = sourceBase.value === 'custom' ? customSourceBase.value : parseInt(sourceBase.value)
    
    if (!validateInput(inputNumber.value, currentBase)) {
      throw new Error(`输入的数值不符合${currentBase}进制格式`)
    }
    
    // 转换为十进制
    const decimalValue = parseInt(inputNumber.value.toUpperCase(), currentBase)
    
    if (isNaN(decimalValue)) {
      throw new Error('无效的数值')
    }
    
    // 转换为各种进制
    results.value = {
      binary: decimalValue.toString(2),
      octal: decimalValue.toString(8),
      decimal: decimalValue,
      hexadecimal: decimalValue.toString(16).toUpperCase()
    }
    
    // 生成计算步骤
    generateCalculationSteps(inputNumber.value, currentBase, decimalValue)
    
    if (!silent) {
      addToHistory({
        toolId: 'base-converter',
        toolName: '进制转换器',
        input: `${inputNumber.value} (${currentBase}进制)`,
        output: `二进制: ${results.value.binary}, 八进制: ${results.value.octal}, 十进制: ${results.value.decimal}, 十六进制: ${results.value.hexadecimal}`,
        metadata: { sourceBase: currentBase }
      })
    }
    
  } catch (error) {
    clearResults()
    if (!silent) {
      console.error('转换失败:', error.message)
    }
  }
}

// 转换到自定义进制
const convertToCustomBase = () => {
  if (!inputNumber.value.trim() || !targetBase.value) return
  
  try {
    const currentBase = sourceBase.value === 'custom' ? customSourceBase.value : parseInt(sourceBase.value)
    
    if (!validateInput(inputNumber.value, currentBase)) {
      throw new Error(`输入的数值不符合${currentBase}进制格式`)
    }
    
    const decimalValue = parseInt(inputNumber.value.toUpperCase(), currentBase)
    customResult.value = decimalValue.toString(targetBase.value).toUpperCase()
    
    addToHistory({
      toolId: 'base-converter',
      toolName: '进制转换器',
      input: `${inputNumber.value} (${currentBase}进制)`,
      output: `${customResult.value} (${targetBase.value}进制)`,
      metadata: { sourceBase: currentBase, targetBase: targetBase.value }
    })
    
  } catch (error) {
    customResult.value = ''
    console.error('自定义转换失败:', error.message)
  }
}

// 生成计算步骤
const generateCalculationSteps = (value, base, result) => {
  const steps = []
  
  if (base === 10) {
    steps.push(`十进制数 ${value} 直接等于 ${result}`)
  } else {
    steps.push(`将 ${base} 进制数 ${value} 转换为十进制：`)
    
    const digits = value.toUpperCase().split('').reverse()
    const calculations = digits.map((digit, index) => {
      const digitValue = parseInt(digit, base)
      const power = index
      const contribution = digitValue * Math.pow(base, power)
      return `${digit} × ${base}^${power} = ${digitValue} × ${Math.pow(base, power)} = ${contribution}`
    })
    
    steps.push(...calculations)
    steps.push(`总和: ${calculations.map(calc => calc.split(' = ')[2]).join(' + ')} = ${result}`)
  }
  
  calculationSteps.value = steps
}

// 使用快速示例
const useQuickExample = (example) => {
  inputNumber.value = example.value
  sourceBase.value = example.base.toString()
  convertNumber()
}

// 交换进制
const swapBases = () => {
  if (sourceBase.value === 'custom') return
  
  const temp = sourceBase.value
  sourceBase.value = targetBase.value.toString()
  targetBase.value = parseInt(temp)
  
  if (inputNumber.value.trim()) {
    convertNumber()
  }
}

// 复制结果
const copyResult = (value) => {
  copyToClipboard(value.toString())
}

// 清空所有
const clearAll = () => {
  inputNumber.value = ''
  clearResults()
  customResult.value = ''
  calculationSteps.value = []
}

// 清空结果
const clearResults = () => {
  results.value = {
    binary: null,
    octal: null,
    decimal: null,
    hexadecimal: null
  }
}
</script>

<style scoped>
.input-section,
.results-section,
.custom-conversion-section,
.calculation-section,
.quick-convert-section,
.binary-bits-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #eab308;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, #fffbeb, #fef3c7);
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #facc15;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.input-group {
  @apply space-y-2;
}

.input-label {
  @apply block text-sm font-medium text-gray-700;
}

.input-field {
  @apply w-full px-3 py-2 border-2 border-yellow-300 rounded-lg focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200;
}

.custom-base-input {
  @apply mt-4;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-secondary,
.btn-helper {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-yellow-500 text-white border-yellow-600 hover:bg-yellow-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-helper {
  @apply bg-blue-500 text-white border-blue-600 hover:bg-blue-600;
}

.btn-icon {
  @apply mr-1;
}

.result-card {
  @apply bg-white p-4 relative;
}

.result-header {
  @apply flex justify-between items-center mb-2;
}

.result-label {
  @apply text-sm font-medium text-gray-700;
}

.copy-btn {
  @apply bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600 transition-colors;
}

.result-value {
  @apply font-mono text-lg font-bold text-gray-800 break-all;
}

.custom-result {
  @apply mt-4;
}

.steps-container {
  @apply space-y-3;
}

.calculation-step {
  @apply flex items-start gap-3;
}

.step-number {
  @apply flex-shrink-0 w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold;
}

.step-content {
  @apply text-sm text-gray-700 font-mono;
}

.quick-convert-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3;
}

.quick-convert-btn {
  @apply bg-white border-2 border-yellow-300 rounded-lg p-3 text-center hover:bg-yellow-50 hover:border-yellow-400 transition-all duration-200;
}

.example-value {
  @apply font-mono font-bold text-gray-800;
}

.example-base {
  @apply text-xs text-gray-500 mt-1;
}

.binary-bits {
  @apply flex justify-center gap-2 mb-4;
}

.binary-bit {
  @apply flex flex-col items-center p-2 border-2 border-gray-300 rounded-lg bg-white;
}

.binary-bit.active {
  @apply border-yellow-500 bg-yellow-50;
}

.bit-value {
  @apply text-lg font-mono font-bold;
}

.bit-position {
  @apply text-xs text-gray-500 mt-1;
}

.bit-calculation {
  @apply text-center;
}

.calculation-formula {
  @apply text-sm font-mono text-gray-700 bg-gray-50 p-3 rounded border;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
  
  .quick-convert-grid {
    @apply grid-cols-2;
  }
  
  .binary-bits {
    @apply gap-1;
  }
  
  .binary-bit {
    @apply p-1;
  }
}
</style>
