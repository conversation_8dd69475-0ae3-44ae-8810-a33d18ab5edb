<template>
  <ToolLayout
    title="JSON格式化器"
    description="美化、压缩、验证JSON数据的在线工具"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入区域 -->
      <div class="input-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📝</span>
          输入JSON数据
        </h2>
        
        <textarea 
          v-model="inputJson"
          class="json-input"
          placeholder="请输入JSON数据..."
          @input="handleInput"
        ></textarea>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button 
            @click="formatJson"
            class="btn-primary"
            :disabled="!inputJson.trim()"
          >
            <span class="btn-icon">✨</span>
            格式化
          </button>
          
          <button 
            @click="compressJson"
            class="btn-success"
            :disabled="!inputJson.trim()"
          >
            <span class="btn-icon">📦</span>
            压缩
          </button>
          
          <button 
            @click="validateJson"
            class="btn-warning"
            :disabled="!inputJson.trim()"
          >
            <span class="btn-icon">✅</span>
            验证
          </button>
          
          <button 
            @click="clearAll"
            class="btn-secondary"
          >
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
      </div>
      
      <!-- 输出区域 -->
      <div class="output-section sketch-border">
        <h2 class="section-title">
          <span class="title-icon">📋</span>
          格式化结果
        </h2>
        
        <ResultDisplay
          :content="outputJson"
          :type="'json'"
          :loading="isProcessing"
          :error="error"
          :allow-copy="true"
          :allow-download="true"
          :download-filename="'formatted.json'"
          :stats="stats"
          @clear="clearOutput"
        />
      </div>
    </div>
    
    <!-- 功能说明 -->
    <div class="mt-8 sketch-border bg-white p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
        <span class="mr-2">💡</span>
        使用说明
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
          <h4 class="font-medium text-gray-800 mb-2">✨ 格式化功能</h4>
          <p>将压缩的JSON数据格式化为易读的缩进格式</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">📦 压缩功能</h4>
          <p>移除JSON中的空格和换行，减小数据体积</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">✅ 验证功能</h4>
          <p>检查JSON语法是否正确，显示错误位置</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-800 mb-2">📋 复制功能</h4>
          <p>一键复制格式化后的结果到剪贴板</p>
        </div>
      </div>
    </div>
  </ToolLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ToolLayout from '@/components/common/ToolLayout.vue'
import ResultDisplay from '@/components/common/ResultDisplay.vue'
import { useClipboard } from '@/composables/useClipboard'
import { useToolHistory } from '@/composables/useToolHistory'

// 响应式数据
const inputJson = ref('')
const outputJson = ref('')
const error = ref('')
const isProcessing = ref(false)

// 组合式函数
const { copyToClipboard } = useClipboard()
const { addToHistory } = useToolHistory()

// 计算属性
const stats = computed(() => {
  if (!outputJson.value) return null
  
  const inputSize = inputJson.value.length
  const outputSize = outputJson.value.length
  const savings = inputSize > 0 ? ((inputSize - outputSize) / inputSize * 100).toFixed(1) : 0
  
  return {
    '输入大小': `${inputSize} 字符`,
    '输出大小': `${outputSize} 字符`,
    '空间节省': `${savings}%`
  }
})

// 防抖函数
let debounceTimer = null
const debounce = (func, delay) => {
  return (...args) => {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => func.apply(this, args), delay)
  }
}

// 实时验证（防抖）
const handleInput = debounce(() => {
  if (inputJson.value.trim()) {
    validateJson(true) // 静默验证
  }
}, 500)

// 格式化JSON
const formatJson = () => {
  if (!inputJson.value.trim()) {
    error.value = '请输入JSON数据'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    const parsed = JSON.parse(inputJson.value)
    outputJson.value = JSON.stringify(parsed, null, 2)
    
    // 添加到历史记录
    addToHistory({
      toolId: 'json-formatter',
      toolName: 'JSON格式化器',
      input: inputJson.value,
      output: outputJson.value,
      metadata: { action: 'format' }
    })
    
  } catch (err) {
    error.value = `JSON格式错误: ${err.message}`
    outputJson.value = ''
  } finally {
    isProcessing.value = false
  }
}

// 压缩JSON
const compressJson = () => {
  if (!inputJson.value.trim()) {
    error.value = '请输入JSON数据'
    return
  }
  
  isProcessing.value = true
  error.value = ''
  
  try {
    const parsed = JSON.parse(inputJson.value)
    outputJson.value = JSON.stringify(parsed)
    
    // 添加到历史记录
    addToHistory({
      toolId: 'json-formatter',
      toolName: 'JSON格式化器',
      input: inputJson.value,
      output: outputJson.value,
      metadata: { action: 'compress' }
    })
    
  } catch (err) {
    error.value = `JSON格式错误: ${err.message}`
    outputJson.value = ''
  } finally {
    isProcessing.value = false
  }
}

// 验证JSON
const validateJson = (silent = false) => {
  if (!inputJson.value.trim()) {
    if (!silent) error.value = '请输入JSON数据'
    return false
  }
  
  try {
    JSON.parse(inputJson.value)
    if (!silent) {
      outputJson.value = '✅ JSON语法验证通过'
      error.value = ''
    }
    return true
  } catch (err) {
    if (!silent) {
      error.value = `JSON格式错误: ${err.message}`
      outputJson.value = `❌ 错误详情:\n${err.message}`
    }
    return false
  }
}

// 清空所有内容
const clearAll = () => {
  inputJson.value = ''
  outputJson.value = ''
  error.value = ''
}

// 清空输出
const clearOutput = () => {
  outputJson.value = ''
  error.value = ''
}

// 加载示例数据
const loadExample = () => {
  const example = {
    "name": "张三",
    "age": 25,
    "city": "北京",
    "hobbies": ["读书", "旅行", "编程"],
    "address": {
      "street": "中关村大街",
      "number": 123
    }
  }
  inputJson.value = JSON.stringify(example)
  formatJson()
}

// 组件挂载时加载示例
onMounted(() => {
  loadExample()
})
</script>

<style scoped>
.input-section,
.output-section {
  @apply bg-white p-6;
}

.sketch-border {
  border: 2px solid #64748b;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
}

.sketch-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px dashed #94a3b8;
  border-radius: 14px;
  pointer-events: none;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 flex items-center;
}

.title-icon {
  @apply mr-2 text-xl;
}

.json-input {
  @apply w-full h-64 p-3 border-2 border-gray-300 rounded-lg font-mono text-sm resize-none;
  @apply focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200;
  background: white;
  transition: border-color 0.3s ease;
}

.action-buttons {
  @apply flex flex-wrap gap-3 mt-4;
}

.btn-primary,
.btn-success,
.btn-warning,
.btn-secondary {
  @apply px-4 py-2 rounded-lg border-2 font-semibold transition-all duration-200;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-500 text-white border-blue-600 hover:bg-blue-600;
}

.btn-success {
  @apply bg-emerald-500 text-white border-emerald-600 hover:bg-emerald-600;
}

.btn-warning {
  @apply bg-amber-500 text-white border-amber-600 hover:bg-amber-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white border-gray-600 hover:bg-gray-600;
}

.btn-icon {
  @apply mr-1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>
