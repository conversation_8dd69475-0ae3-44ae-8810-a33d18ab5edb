import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Tool<PERSON>ontainer from '@/views/ToolContainer.vue'
import { tools } from '@/data/tools.js'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/tool/:toolId',
    name: 'Tool',
    component: ToolContainer,
    props: true
  }
]

// 为每个工具添加直接路由
tools.forEach(tool => {
  if (tool.path) {
    routes.push({
      path: tool.path,
      name: `Tool-${tool.id}`,
      component: ToolContainer,
      props: { toolId: tool.id }
    })
  }
})

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router