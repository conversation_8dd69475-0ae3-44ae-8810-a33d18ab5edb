import { ref, watch, nextTick } from 'vue'

/**
 * 响应式 localStorage 组合式函数
 * @param {string} key - 存储键名
 * @param {any} defaultValue - 默认值
 * @param {Object} options - 配置选项
 * @returns {Object} 响应式存储对象
 */
export function useLocalStorage(key, defaultValue = null, options = {}) {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify
    },
    syncAcrossTabs = true,
    onError = (error) => console.error('localStorage error:', error)
  } = options

  const storedValue = ref(defaultValue)

  /**
   * 从 localStorage 读取值
   */
  const read = () => {
    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return defaultValue
      }
      return serializer.read(item)
    } catch (error) {
      onError(error)
      return defaultValue
    }
  }

  /**
   * 写入值到 localStorage
   * @param {any} value - 要存储的值
   */
  const write = (value) => {
    try {
      if (value === null || value === undefined) {
        localStorage.removeItem(key)
      } else {
        localStorage.setItem(key, serializer.write(value))
      }
    } catch (error) {
      onError(error)
    }
  }

  /**
   * 删除存储的值
   */
  const remove = () => {
    try {
      localStorage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      onError(error)
    }
  }

  // 初始化值
  storedValue.value = read()

  // 监听值变化并同步到 localStorage
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )

  // 跨标签页同步
  if (syncAcrossTabs) {
    const handleStorageChange = (e) => {
      if (e.key === key && e.newValue !== serializer.write(storedValue.value)) {
        try {
          storedValue.value = e.newValue ? serializer.read(e.newValue) : defaultValue
        } catch (error) {
          onError(error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('storage', handleStorageChange)
    }

    // 返回清理函数
    return {
      value: storedValue,
      remove,
      cleanup
    }
  }

  return {
    value: storedValue,
    remove
  }
}

/**
 * 简化的 localStorage 工具函数
 */
export const localStorage = {
  /**
   * 设置值
   * @param {string} key - 键名
   * @param {any} value - 值
   */
  set(key, value) {
    try {
      window.localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('localStorage.set error:', error)
    }
  },

  /**
   * 获取值
   * @param {string} key - 键名
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值或默认值
   */
  get(key, defaultValue = null) {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('localStorage.get error:', error)
      return defaultValue
    }
  },

  /**
   * 删除值
   * @param {string} key - 键名
   */
  remove(key) {
    try {
      window.localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage.remove error:', error)
    }
  },

  /**
   * 清空所有值
   */
  clear() {
    try {
      window.localStorage.clear()
    } catch (error) {
      console.error('localStorage.clear error:', error)
    }
  },

  /**
   * 获取所有键名
   * @returns {Array<string>} 键名数组
   */
  keys() {
    try {
      return Object.keys(window.localStorage)
    } catch (error) {
      console.error('localStorage.keys error:', error)
      return []
    }
  },

  /**
   * 检查是否存在某个键
   * @param {string} key - 键名
   * @returns {boolean} 是否存在
   */
  has(key) {
    try {
      return window.localStorage.getItem(key) !== null
    } catch (error) {
      console.error('localStorage.has error:', error)
      return false
    }
  },

  /**
   * 获取存储大小（近似值）
   * @returns {number} 存储大小（字节）
   */
  size() {
    try {
      let total = 0
      for (const key in window.localStorage) {
        if (window.localStorage.hasOwnProperty(key)) {
          total += window.localStorage[key].length + key.length
        }
      }
      return total
    } catch (error) {
      console.error('localStorage.size error:', error)
      return 0
    }
  }
}

/**
 * 工具设置管理
 * @param {string} toolId - 工具ID
 * @returns {Object} 工具设置管理对象
 */
export function useToolSettings(toolId) {
  const settingsKey = `tool_settings_${toolId}`
  
  const { value: settings, remove } = useLocalStorage(settingsKey, {})

  /**
   * 获取设置值
   * @param {string} key - 设置键名
   * @param {any} defaultValue - 默认值
   * @returns {any} 设置值
   */
  const getSetting = (key, defaultValue = null) => {
    return settings.value[key] ?? defaultValue
  }

  /**
   * 设置值
   * @param {string} key - 设置键名
   * @param {any} value - 设置值
   */
  const setSetting = (key, value) => {
    settings.value[key] = value
  }

  /**
   * 删除设置
   * @param {string} key - 设置键名
   */
  const removeSetting = (key) => {
    delete settings.value[key]
  }

  /**
   * 重置所有设置
   */
  const resetSettings = () => {
    settings.value = {}
  }

  /**
   * 导出设置
   * @returns {Object} 设置对象
   */
  const exportSettings = () => {
    return { ...settings.value }
  }

  /**
   * 导入设置
   * @param {Object} newSettings - 新设置对象
   */
  const importSettings = (newSettings) => {
    settings.value = { ...newSettings }
  }

  return {
    settings,
    getSetting,
    setSetting,
    removeSetting,
    resetSettings,
    exportSettings,
    importSettings,
    remove
  }
}

/**
 * 用户偏好设置
 */
export function useUserPreferences() {
  const { value: preferences } = useLocalStorage('user_preferences', {
    theme: 'light',
    language: 'zh-CN',
    autoSave: true,
    showTips: true,
    compactMode: false
  })

  const setPreference = (key, value) => {
    preferences.value[key] = value
  }

  const getPreference = (key, defaultValue = null) => {
    return preferences.value[key] ?? defaultValue
  }

  const resetPreferences = () => {
    preferences.value = {
      theme: 'light',
      language: 'zh-CN',
      autoSave: true,
      showTips: true,
      compactMode: false
    }
  }

  return {
    preferences,
    setPreference,
    getPreference,
    resetPreferences
  }
}
