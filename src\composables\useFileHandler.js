import { ref } from 'vue'

export function useFileHandler() {
  const isLoading = ref(false)
  const error = ref('')
  const progress = ref(0)

  /**
   * 读取文件内容
   * @param {File} file - 文件对象
   * @param {string} type - 读取类型: 'text', 'dataURL', 'arrayBuffer', 'binaryString'
   * @returns {Promise<string|ArrayBuffer>}
   */
  const readFile = (file, type = 'text') => {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('没有选择文件'))
        return
      }

      isLoading.value = true
      error.value = ''
      progress.value = 0

      const reader = new FileReader()

      reader.onload = (e) => {
        isLoading.value = false
        progress.value = 100
        resolve(e.target.result)
      }

      reader.onerror = () => {
        isLoading.value = false
        const errorMsg = '文件读取失败'
        error.value = errorMsg
        reject(new Error(errorMsg))
      }

      reader.onprogress = (e) => {
        if (e.lengthComputable) {
          progress.value = Math.round((e.loaded / e.total) * 100)
        }
      }

      // 根据类型选择读取方法
      switch (type) {
        case 'text':
          reader.readAsText(file, 'UTF-8')
          break
        case 'dataURL':
          reader.readAsDataURL(file)
          break
        case 'arrayBuffer':
          reader.readAsArrayBuffer(file)
          break
        case 'binaryString':
          reader.readAsBinaryString(file)
          break
        default:
          reader.readAsText(file, 'UTF-8')
      }
    })
  }

  /**
   * 读取多个文件
   * @param {FileList} files - 文件列表
   * @param {string} type - 读取类型
   * @returns {Promise<Array>}
   */
  const readMultipleFiles = async (files, type = 'text') => {
    const results = []
    const fileArray = Array.from(files)

    for (let i = 0; i < fileArray.length; i++) {
      try {
        const content = await readFile(fileArray[i], type)
        results.push({
          name: fileArray[i].name,
          size: fileArray[i].size,
          type: fileArray[i].type,
          content: content
        })
      } catch (err) {
        results.push({
          name: fileArray[i].name,
          error: err.message
        })
      }
    }

    return results
  }

  /**
   * 下载文件
   * @param {string|Blob} content - 文件内容
   * @param {string} filename - 文件名
   * @param {string} mimeType - MIME 类型
   */
  const downloadFile = (content, filename, mimeType = 'text/plain') => {
    try {
      let blob
      
      if (content instanceof Blob) {
        blob = content
      } else {
        blob = new Blob([content], { type: mimeType })
      }

      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.style.display = 'none'
      
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      
      // 清理 URL 对象
      setTimeout(() => URL.revokeObjectURL(url), 100)
    } catch (err) {
      error.value = '下载失败: ' + err.message
      throw err
    }
  }

  /**
   * 下载 JSON 文件
   * @param {any} data - 要下载的数据
   * @param {string} filename - 文件名
   * @param {number} indent - JSON 缩进
   */
  const downloadJson = (data, filename = 'data.json', indent = 2) => {
    try {
      const jsonString = JSON.stringify(data, null, indent)
      downloadFile(jsonString, filename, 'application/json')
    } catch (err) {
      error.value = 'JSON 序列化失败: ' + err.message
      throw err
    }
  }

  /**
   * 下载 CSV 文件
   * @param {Array<Array>} data - 二维数组数据
   * @param {string} filename - 文件名
   * @param {string} separator - 分隔符
   */
  const downloadCsv = (data, filename = 'data.csv', separator = ',') => {
    try {
      const csvContent = data.map(row => 
        row.map(cell => {
          // 处理包含分隔符或引号的单元格
          const cellStr = String(cell)
          if (cellStr.includes(separator) || cellStr.includes('"') || cellStr.includes('\n')) {
            return `"${cellStr.replace(/"/g, '""')}"`
          }
          return cellStr
        }).join(separator)
      ).join('\n')
      
      downloadFile(csvContent, filename, 'text/csv')
    } catch (err) {
      error.value = 'CSV 生成失败: ' + err.message
      throw err
    }
  }

  /**
   * 验证文件类型
   * @param {File} file - 文件对象
   * @param {Array<string>} allowedTypes - 允许的文件类型
   * @returns {boolean}
   */
  const validateFileType = (file, allowedTypes) => {
    if (!file || !allowedTypes || allowedTypes.length === 0) {
      return false
    }

    const fileType = file.type.toLowerCase()
    const fileName = file.name.toLowerCase()

    return allowedTypes.some(type => {
      if (type.startsWith('.')) {
        // 扩展名匹配
        return fileName.endsWith(type.toLowerCase())
      } else {
        // MIME 类型匹配
        return fileType === type.toLowerCase() || fileType.startsWith(type.toLowerCase() + '/')
      }
    })
  }

  /**
   * 验证文件大小
   * @param {File} file - 文件对象
   * @param {number} maxSize - 最大文件大小（字节）
   * @returns {boolean}
   */
  const validateFileSize = (file, maxSize) => {
    return file && file.size <= maxSize
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string}
   */
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }

  /**
   * 重置状态
   */
  const reset = () => {
    isLoading.value = false
    error.value = ''
    progress.value = 0
  }

  return {
    isLoading,
    error,
    progress,
    readFile,
    readMultipleFiles,
    downloadFile,
    downloadJson,
    downloadCsv,
    validateFileType,
    validateFileSize,
    formatFileSize,
    clearError,
    reset
  }
}
